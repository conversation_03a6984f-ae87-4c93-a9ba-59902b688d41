# DingDing Agent - LangGraph Supervisor架构

这是一个基于LangGraph Supervisor模式的多智能体协调系统，集成了钉钉办公功能和Text2SQL智能查询能力。

## 🎯 项目概述

本项目已完成从传统LangChain Agent架构到LangGraph Supervisor架构的重构，实现了：

- **多智能体协调**：使用Supervisor模式管理不同专业智能体
- **钉钉集成**：完整的日程管理、待办事项、会议安排功能
- **Text2SQL能力**：自然语言转SQL查询和执行
- **情绪感知**：动态情绪检测和响应调整
- **知识库检索**：RAG功能支持

## 🏗️ 架构设计

### Supervisor工作流
```
用户输入 → Supervisor → 路由决策 → 专业智能体 → 结果返回
                ↓
        [DingTalk Agent]  [Text2SQL Agent]  [RAG Agent]  [Search Agent]
```

### 核心组件

1. **Supervisor智能体** (`supervisor_workflow.py`)
   - 负责请求路由和智能体协调
   - 支持多轮对话和上下文管理
   - 集成情绪状态感知

2. **DingTalk智能体** (`Tools.py`)
   - 日程管理：创建、查询、修改、删除
   - 待办事项：创建和管理
   - 忙闲查询：时间冲突检测

3. **Text2SQL智能体** (`text2sql_integration.py`)
   - 自然语言转SQL
   - 并发检索优化
   - 结果自然语言解释

4. **情绪检测** (`Emotion.py`)
   - 实时情绪分析
   - 多种情绪类型支持
   - 情绪强度评分

5. **记忆管理** (`Memory.py`)
   - Redis持久化存储
   - 自动摘要长对话
   - 用户会话管理

## 🚀 快速开始

### 环境配置

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置环境变量（创建`.env`文件）：
```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=your_api_base_url
BASE_MODEL=gpt-4
BACKUP_MODEL=deepseek-chat

# 钉钉配置
DINGDING_ID=your_dingtalk_app_id
DINGDING_SECRET=your_dingtalk_app_secret
DINGDING_UNION_ID=your_union_id

# 其他配置
MEMORY_KEY=chat_history
REDIS_URL=redis://localhost:6380/0
SERPAPI_API_KEY=your_serpapi_key
```

### 运行测试

```bash
# 基础功能测试
python test_agent.py

# 全面系统测试
python comprehensive_test.py
```

### 启动钉钉机器人

```bash
python -m dingding.DingWebHook
```

## 📁 项目结构

```
dingding-agent/
├── dingding/                    # 主要模块
│   ├── Agents.py               # 重构后的智能体类
│   ├── supervisor_workflow.py  # LangGraph Supervisor工作流
│   ├── Emotion.py              # 情绪检测模块
│   ├── Tools.py                # 钉钉工具集
│   ├── Memory.py               # 记忆管理
│   ├── Prompt.py               # 提示词管理
│   ├── Storage.py              # 用户存储
│   ├── DingWebHook.py          # 钉钉Webhook处理
│   └── text2sql_integration.py # Text2SQL集成
├── text2sql/                   # Text2SQL组件
│   ├── text2sql_agent.py       # Text2SQL智能体
│   ├── config.py               # 配置管理
│   └── data/                   # 数据文件
├── test_agent.py               # 基础测试
├── comprehensive_test.py       # 全面测试
└── README.md                   # 项目文档
```

## 🔧 主要功能

### 1. 智能路由
Supervisor根据用户输入自动路由到合适的专业智能体：
- 钉钉相关 → DingTalk Agent
- 数据查询 → Text2SQL Agent
- 知识问答 → RAG Agent
- 实时信息 → Search Agent

### 2. 钉钉功能
- ✅ 创建日程：`SetSchedule`
- ✅ 查询日程：`SearchSchedule`
- ✅ 修改日程：`ModifySchedule`
- ✅ 删除日程：`DelSchedule`
- ✅ 创建待办：`create_todo`
- ✅ 忙闲查询：`checkSchedule`

### 3. Text2SQL功能
- ✅ 自然语言转SQL
- ✅ 并发检索优化
- ✅ SQL执行和结果解释
- ✅ 错误修复重试

### 4. 情绪感知
- ✅ 实时情绪检测
- ✅ 6种情绪类型：default, upbeat, angry, cheerful, depressed, friendly
- ✅ 情绪强度评分（1-10）
- ✅ 动态响应调整

## 🧪 测试覆盖

- ✅ 模块导入测试
- ✅ Supervisor工作流测试
- ✅ Text2SQL集成测试
- ✅ 情绪检测测试
- ✅ 工具功能测试
- ✅ AgentClass功能测试

## 🔄 重构亮点

### 从传统Agent到LangGraph Supervisor

**重构前**：
- 单一Agent处理所有请求
- 静态工具配置
- 简单的情绪检测

**重构后**：
- 多智能体协调架构
- 动态路由和专业化处理
- 完整的情绪感知系统
- 模块化设计，易于扩展

### 关键改进

1. **架构升级**：从单Agent到Supervisor多Agent架构
2. **功能完善**：补全缺失的EmotionClass和get_info_from_local工具
3. **集成优化**：Text2SQL组件无缝集成
4. **错误处理**：完善的异常处理和降级机制
5. **测试覆盖**：全面的测试用例和验证

## 🚧 后续计划

- [ ] 完善Text2SQL的真实数据库连接
- [ ] 增加更多钉钉API功能
- [ ] 优化情绪检测准确性
- [ ] 添加更多专业智能体
- [ ] 性能监控和日志系统

## 📝 使用示例

```python
from dingding.Agents import AgentClass

# 初始化智能体
agent = AgentClass()

# 处理用户请求
response = agent.run_agent("帮我创建明天下午2点的会议", user_id="user123")
print(response['output'])

# 查看情绪状态
emotion = agent.get_emotion_status()
print(f"当前情绪: {emotion}")
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License
"""
情绪分析模块

负责分析用户输入的情绪，并选择相应的数字人动作和语音风格。
支持的情绪类型：
- default: 中性、平静的情绪状态
- upbeat: 积极向上、充满活力的情绪
- angry: 愤怒、生气的情绪
- cheerful: 开心愉快、充满欢乐的情绪
- depressed: 沮丧、压抑的情绪
- friendly: 友好、亲切的情绪
"""

from typing import Dict, Any, List, Tuple
from pydantic import BaseModel, Field
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import logging

logger = logging.getLogger(__name__)


class EmotionResult(BaseModel):
    """情绪分析结果模型"""
    feeling: str = Field(..., title="情绪分类", description="用户输入的情绪分类")
    action: str = Field(..., title="动作", description="根据用户输入以及判断的情绪，挑选一个合适的动作")
    confidence: float = Field(default=0.8, title="置信度", description="情绪识别的置信度")


class EmotionAnalyzer:
    """情绪分析器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化情绪分析器
        
        Args:
            config: 配置字典，包含LLM配置信息
        """
        self.config = config
        
        # 初始化LLM
        llm_config = config.get("llm", {})
        self.model = ChatOpenAI(
            model=llm_config.get("model_name", "gpt-4o-mini"),
            api_key=llm_config.get("api_key"),
            base_url=llm_config.get("base_url"),
            temperature=0.3  # 情绪分析需要较低的温度以保证一致性
        )
        
        # 情绪到动作的映射
        self.emotion_actions = {
            "default": ["show-front-1", "show-front-2", "show-front-3"],
            "upbeat": ["thumbsup-left-1", "show-front-4", "show-front-5"],
            "angry": ["show-front-6", "think-twice-1"],
            "cheerful": ["show-front-7", "show-front-8", "show-front-9"],
            "depressed": ["think-twice-1", "show-front-1"],
            "friendly": ["show-front-2", "show-front-3", "thumbsup-left-1"]
        }
        
        # 创建情绪分析提示模板
        self.emotion_prompt = ChatPromptTemplate.from_template("""
分析用户输入，参考下面的列表，判断用户的情绪分类，返回一个对应单词：

情绪类型对照：
- default: 中性、平静的情绪状态
- upbeat: 积极向上、充满活力的情绪
- angry: 愤怒、生气的情绪
- cheerful: 开心愉快、充满欢乐的情绪
- depressed: 沮丧、压抑的情绪
- friendly: 友好、亲切的情绪

情绪分类指南：
1. default: 用于表达中性或普通的情绪状态
2. upbeat: 用于表达积极向上、充满干劲的状态
3. angry: 用于表达愤怒、不满、生气的情绪
4. cheerful: 用于表达欢快、喜悦的情绪
5. depressed: 用于表达消极、低落、压抑的情绪
6. friendly: 用于表达友善、亲切的情绪

根据用户输入以及判断的情绪，挑选一个合适的动作，相同名字的可以随机选一个，返回给用户。
可选动作列表：
- numeric1-left-1, numeric2-left-1, numeric3-left-1
- thumbsup-left-1
- show-front-1, show-front-2, show-front-3, show-front-4, show-front-5
- show-front-6, show-front-7, show-front-8, show-front-9
- think-twice-1

记住：值可以返回动作的英文名字，例如：numeric1-left-1，不可以有其他的内容。

用户输入: {user_input}

{format_instructions}
""")
        
        # 创建输出解析器
        self.parser = PydanticOutputParser(pydantic_object=EmotionResult)
        
        # 创建情绪分析链
        self.emotion_chain = self.emotion_prompt | self.model | self.parser
        
    async def analyze_emotion(self, user_input: str) -> EmotionResult:
        """
        分析用户输入的情绪
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            情绪分析结果
        """
        try:
            result = await self.emotion_chain.ainvoke({
                "user_input": user_input,
                "format_instructions": self.parser.get_format_instructions()
            })
            
            # 验证情绪类型是否有效
            if result.feeling not in self.emotion_actions:
                logger.warning(f"未知情绪类型: {result.feeling}，使用默认情绪")
                result.feeling = "default"
                result.action = self.emotion_actions["default"][0]
            
            logger.info(f"情绪分析结果 - 情绪: {result.feeling}, 动作: {result.action}")
            return result
            
        except Exception as e:
            logger.error(f"情绪分析失败: {e}")
            # 返回默认情绪结果
            return EmotionResult(
                feeling="default",
                action="show-front-1",
                confidence=0.5
            )
    
    def get_voice_style(self, emotion: str) -> str:
        """
        根据情绪获取语音风格
        
        Args:
            emotion: 情绪类型
            
        Returns:
            语音风格字符串
        """
        voice_styles = {
            "default": "chat",
            "upbeat": "excited",
            "angry": "angry",
            "cheerful": "cheerful",
            "depressed": "sad",
            "friendly": "friendly"
        }
        
        return voice_styles.get(emotion, "chat")
    
    def get_emotion_description(self, emotion: str) -> str:
        """
        获取情绪的中文描述
        
        Args:
            emotion: 情绪类型
            
        Returns:
            情绪的中文描述
        """
        descriptions = {
            "default": "平静",
            "upbeat": "积极向上",
            "angry": "愤怒",
            "cheerful": "开心愉快",
            "depressed": "沮丧",
            "friendly": "友好亲切"
        }
        
        return descriptions.get(emotion, "平静")

    def get_emotion_actions(self) -> Dict[str, List[str]]:
        """
        获取情绪对应的动作列表

        Returns:
            情绪到动作列表的映射
        """
        return self.emotion_actions.copy()

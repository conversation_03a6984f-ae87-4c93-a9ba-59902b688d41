import os
import logging
import re
import time
import asyncio
import concurrent.futures
from typing import Dict, Any, List, Tuple, Optional
from pymilvus import MilvusClient
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from sqlalchemy import create_engine, text
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage

# 导入配置 - 支持相对导入和绝对导入
try:
    # 尝试相对导入（作为包模块运行时）
    from .config import (
        MYSQL_DB_URL, MILVUS_HOST, MILVUS_PORT,
        OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MODEL, OPENAI_EMBEDDING_MODEL,
        AGENT_MAX_RETRIES, AGENT_TIMEOUT, LOG_LEVEL
    )
except ImportError:
    # 回退到绝对导入（直接运行脚本时）
    import sys
    import os
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from config import (
        MYSQL_DB_URL, MILVUS_HOST, MILVUS_PORT,
        OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MODEL, OPENAI_EMBEDDING_MODEL,
        AGENT_MAX_RETRIES, AGENT_TIMEOUT, LOG_LEVEL
    )

# 环境配置
logging.basicConfig(level=getattr(logging, LOG_LEVEL.upper()),
                   format='%(asctime)s - %(levelname)s - %(message)s')

class Text2SQLAgent:
    def __init__(self):
        # 初始化组件
        self.model = ChatOpenAI(
            model=OPENAI_MODEL,
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL,
        )

        # 初始化嵌入函数
        try:
            self.embedding_fn = OpenAIEmbeddings(
                api_key=OPENAI_API_KEY,
                base_url=OPENAI_BASE_URL,
                model=OPENAI_EMBEDDING_MODEL
            )
            logging.info("嵌入函数初始化成功")

        except Exception as e:
            logging.error(f"嵌入函数初始化失败：{e}")
            raise

        # 初始化Milvus客户端
        try:
            self.milvus_client = MilvusClient(
                host=MILVUS_HOST,
                port=MILVUS_PORT
            )
            logging.info("Milvus客户端初始化成功")
        except Exception as e:
            logging.error(f"Milvus客户端初始化失败：{e}")
            raise

        # 初始化数据库连接
        try:
            self.engine = create_engine(MYSQL_DB_URL)
            logging.info("数据库连接初始化成功")
        except Exception as e:
            logging.error(f"数据库连接初始化失败：{e}")
            raise
        
        # 创建工具列表
        self.tools = self._create_tools()

        # 创建智能体
        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            name="text2sql_expert",
            debug=True,
            prompt="""你是一个专业的Text2SQL专家，能够将自然语言查询转换为SQL语句并执行，最后提供易懂的结果解释。

你的工作流程（高效并发模式）：
1. 如果遇到嵌入相关错误，首先使用diagnose_embedding_tool诊断问题
2. 使用concurrent_retrieve_all_tool并发检索所有相关信息（数据库结构DDL、相似查询示例、字段描述信息）
3. 使用generate_sql_tool基于检索到的信息生成准确的SQL语句
4. 使用execute_sql_tool执行SQL并获取结构化结果
5. 使用convert_result_to_natural_language_tool将查询结果转换为易懂的自然语言描述
6. 如果SQL执行失败，使用fix_sql_tool修复错误，然后重新执行

关键特点：
- 并发检索显著提升性能（相比串行检索提升2-3倍速度）
- 一次性获取所有必要的上下文信息
- 减少总体响应时间，提供更好的用户体验

请严格按照这个流程工作，确保：
- 始终使用concurrent_retrieve_all_tool进行信息检索
- 生成的SQL语句准确且可执行
- 最终提供易懂的自然语言结果解释
- 遇到错误时能够自动修复并重试"""
        )
        
        # 创建状态图
        self.workflow = self.create_workflow()
    
    def get_agent(self):
        """获取智能体实例"""
        return self.agent
    
    def get_tools(self):
        """获取工具列表"""
        return self.tools
    
    def get_model(self):
        """获取模型实例"""
        return self.model

    def _preload_collections(self):
        """预加载Milvus集合以提高性能"""
        collections = ["ddl_knowledge", "q2sql_knowledge", "dbdesc_knowledge"]
        for collection in collections:
            try:
                self.milvus_client.load_collection(collection)
                logging.info(f"成功预加载集合：{collection}")
            except Exception as e:
                logging.warning(f"预加载集合 {collection} 失败：{e}")
                # 不抛出异常，允许运行时加载

    def _concurrent_retrieve_single(self, collection_name: str, question: str,
                                  limit: int, output_fields: List[str]) -> Tuple[str, Any]:
        """单个检索操作（用于并发执行）"""
        try:
            # 生成嵌入向量
            q_emb = self.embedding_fn.embed_query(question.strip())

            # 执行搜索
            results = self.milvus_client.search(
                collection_name=collection_name,
                data=[q_emb],
                limit=limit,
                output_fields=output_fields
            )

            if results and len(results) > 0:
                return collection_name, results[0]
            else:
                return collection_name, []

        except Exception as e:
            logging.error(f"并发检索{collection_name}时出错：{str(e)}")
            return collection_name, None

    def concurrent_retrieve_all(self, question: str, max_workers: int = 3) -> Dict[str, Any]:
        """并发执行所有检索操作"""
        if not question or not question.strip():
            return {"error": "问题不能为空"}

        # 定义检索任务配置
        retrieval_tasks = [
            ("ddl_knowledge", 3, ["ddl_text"]),
            ("q2sql_knowledge", 3, ["question", "sql_text"]),
            ("dbdesc_knowledge", 5, ["table_name", "column_name", "description"])
        ]

        results = {}

        # 使用线程池并发执行检索
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有检索任务
            future_to_collection = {
                executor.submit(
                    self._concurrent_retrieve_single,
                    collection_name,
                    question,
                    limit,
                    output_fields
                ): collection_name
                for collection_name, limit, output_fields in retrieval_tasks
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_collection):
                collection_name = future_to_collection[future]
                try:
                    result_collection, result_data = future.result()
                    results[result_collection] = result_data
                    logging.info(f"并发检索完成：{result_collection}")
                except Exception as e:
                    logging.error(f"并发检索{collection_name}失败：{e}")
                    results[collection_name] = None

        return results

    def format_concurrent_results(self, results: Dict[str, Any]) -> Dict[str, str]:
        """格式化并发检索结果"""
        formatted = {}

        # 格式化DDL结果
        if "ddl_knowledge" in results and results["ddl_knowledge"]:
            ddl_hits = results["ddl_knowledge"]
            ddl_context = "\n".join(hit.get("ddl_text", "") for hit in ddl_hits)
            formatted["ddl"] = f"检索到的数据库结构信息：\n{ddl_context}"
        else:
            formatted["ddl"] = "没有找到相关的数据库结构信息"

        # 格式化示例结果
        if "q2sql_knowledge" in results and results["q2sql_knowledge"]:
            example_hits = results["q2sql_knowledge"]
            examples = "\n".join(
                f"问题：{hit.get('question', '')}\nSQL：{hit.get('sql_text', '')}\n"
                for hit in example_hits
            )
            formatted["examples"] = f"检索到的相似示例：\n{examples}"
        else:
            formatted["examples"] = "没有找到相关的查询示例"

        # 格式化描述结果
        if "dbdesc_knowledge" in results and results["dbdesc_knowledge"]:
            desc_hits = results["dbdesc_knowledge"]
            descriptions = "\n".join(
                f"{hit.get('table_name', '')}.{hit.get('column_name', '')}: {hit.get('description', '')}"
                for hit in desc_hits
            )
            formatted["descriptions"] = f"检索到的字段描述：\n{descriptions}"
        else:
            formatted["descriptions"] = "没有找到相关的字段描述"

        return formatted
    
    def _create_tools(self):
        """创建工具列表"""
        from langchain_core.tools import tool

        # 预加载Milvus集合以提高性能
        self._preload_collections()



        @tool
        def concurrent_retrieve_all_tool(question: str) -> str:
            """并发检索所有相关信息（DDL、示例、字段描述）"""
            try:
                # 执行并发检索
                start_time = time.time()
                results = self.concurrent_retrieve_all(question)
                end_time = time.time()

                if "error" in results:
                    return results["error"]

                # 格式化结果
                formatted = self.format_concurrent_results(results)

                # 合并所有检索结果
                combined_result = f"""并发检索完成（耗时：{end_time - start_time:.2f}秒）

{formatted['ddl']}

{formatted['examples']}

{formatted['descriptions']}"""

                return combined_result

            except Exception as e:
                logging.error(f"并发检索失败：{str(e)}")
                return f"并发检索失败：{str(e)}"

        @tool
        def generate_sql_tool(context: str, question: str) -> str:
            """基于上下文和问题生成SQL语句"""
            prompt = f"""基于以下信息生成SQL语句：

{context}

用户问题：{question}

请只返回SQL语句，不要包含任何解释或说明。"""

            response = self.model.invoke(prompt)
            sql = self.extract_sql(response.content)
            return f"生成的SQL语句：{sql}"

        @tool
        def execute_sql_tool(sql: str) -> str:
            """执行SQL语句并返回结构化结果"""
            try:
                # 清理SQL语句
                clean_sql = sql.strip().rstrip(';')
                if not clean_sql:
                    return "SQL语句为空"

                with self.engine.connect() as conn:
                    result = conn.execute(text(clean_sql))
                    cols = list(result.keys())
                    rows = result.fetchall()

                    # 转换为字典列表格式，便于后续处理
                    data = []
                    for row in rows:
                        row_dict = {}
                        for i, col in enumerate(cols):
                            # 处理不同数据类型
                            value = row[i]
                            if value is None:
                                row_dict[col] = None
                            elif isinstance(value, (int, float, str, bool)):
                                row_dict[col] = value
                            else:
                                row_dict[col] = str(value)
                        data.append(row_dict)

                    # 返回结构化结果
                    return f"SQL执行成功！\n查询结果：{len(data)}行数据\n列名：{cols}\n数据：{data}"

            except Exception as e:
                error_msg = str(e)
                logging.error(f"SQL执行失败：{error_msg}")
                return f"SQL执行失败：{error_msg}"

        @tool
        def convert_result_to_natural_language_tool(sql_result: str, original_question: str) -> str:
            """将SQL查询结果转换为自然语言描述"""
            try:
                # 解析SQL执行结果
                if "SQL执行失败" in sql_result:
                    return f"查询执行失败，无法生成自然语言描述。错误信息：{sql_result}"

                # 提取数据信息
                import re
                import json

                # 尝试从结果中提取数据 - 改进的解析逻辑
                data_match = re.search(r'数据：(\[.*\])', sql_result, re.DOTALL)
                row_count_match = re.search(r'查询结果：(\d+)行数据', sql_result)
                cols_match = re.search(r'列名：(\[.*?\])', sql_result)

                if not data_match or not row_count_match:
                    # 如果无法解析，尝试直接处理整个结果
                    return f"根据查询「{original_question}」，查询结果：{sql_result}"

                try:
                    # 使用更安全的JSON解析
                    data_str = data_match.group(1)
                    # 替换单引号为双引号以符合JSON格式
                    data_str = data_str.replace("'", '"')
                    data = json.loads(data_str)
                    row_count = int(row_count_match.group(1))
                    cols = json.loads(cols_match.group(1).replace("'", '"')) if cols_match else []
                except Exception as parse_error:
                    logging.error(f"解析查询结果失败：{parse_error}")
                    return f"根据查询「{original_question}」，查询结果：{sql_result}"

                # 生成自然语言描述
                if row_count == 0:
                    return f"根据查询「{original_question}」，没有找到匹配的数据。"

                # 构建自然语言描述的提示
                data_preview = data[:5] if len(data) > 5 else data
                prompt = f"""请将以下SQL查询结果转换为自然、易懂的中文描述：

原始问题：{original_question}
查询结果数量：{row_count}行
列名：{cols}
数据预览：{data_preview}

要求：
1. 用自然的中文描述查询结果
2. 如果数据较多，提供概要信息
3. 突出关键数据和趋势
4. 保持简洁明了
5. 避免使用技术术语

请直接返回自然语言描述。"""

                response = self.model.invoke(prompt)
                natural_description = response.content.strip()

                return f"查询结果解释：{natural_description}"

            except Exception as e:
                logging.error(f"结果转换失败：{str(e)}")
                return f"结果转换为自然语言时出错：{str(e)}"

        @tool
        def fix_sql_tool(sql: str, error_message: str, context: str) -> str:
            """修复SQL语句中的错误"""
            prompt = f"""SQL语句执行失败，请修复错误：

原始SQL：{sql}
错误信息：{error_message}
数据库上下文：{context}

请分析错误原因并生成修正后的SQL语句。只返回修正后的SQL，不要包含解释。"""

            response = self.model.invoke(prompt)
            fixed_sql = self.extract_sql(response.content)
            return f"修复后的SQL语句：{fixed_sql}"

        @tool
        def diagnose_embedding_tool() -> str:
            """诊断嵌入API连接问题"""
            try:
                # 检查环境变量
                api_key = os.getenv("OPENAI_API_KEY")
                base_url = os.getenv("OPENAI_BASE_URL")
                model = os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-3-large")

                diagnosis = f"""
环境变量检查：
- API_KEY: {'已设置' if api_key else '未设置'}
- BASE_URL: {base_url or '未设置'}
- MODEL: {model}

"""

                if not api_key:
                    return diagnosis + "错误：API密钥未设置"

                if not base_url:
                    return diagnosis + "错误：API地址未设置"

                # 测试简单的嵌入调用
                try:
                    test_embedding = self.embedding_fn.embed_query("test")
                    diagnosis += f"嵌入测试成功，向量维度：{len(test_embedding)}"
                    return diagnosis
                except Exception as e:
                    diagnosis += f"嵌入测试失败：{str(e)}"
                    return diagnosis

            except Exception as e:
                return f"诊断失败：{str(e)}"

        return [
            diagnose_embedding_tool,
            concurrent_retrieve_all_tool,  # 统一的并发检索工具
            generate_sql_tool,
            execute_sql_tool,
            convert_result_to_natural_language_tool,
            fix_sql_tool
        ]

    def extract_sql(self, text: str) -> str:
        """从文本中提取SQL语句"""
        # 尝试匹配 SQL 代码块
        sql_blocks = re.findall(r'```sql\n(.*?)\n```', text, re.DOTALL)
        if sql_blocks:
            return sql_blocks[0].strip()

        # 如果没有找到代码块，尝试匹配 SELECT 语句
        select_match = re.search(r'SELECT.*?;', text, re.DOTALL)
        if select_match:
            return select_match.group(0).strip()

        # 如果都没有找到，返回原始文本
        return text.strip()
    
    def create_workflow(self) -> StateGraph:
        """创建工作流程"""
        workflow = StateGraph(Dict[str, Any])
        
        # 添加节点
        workflow.add_node("agent", self.agent)
        
        # 设置入口和出口
        workflow.set_entry_point("agent")
        workflow.add_edge("agent", END)
        
        return workflow.compile()
    
    def process_query(self, question: str) -> Dict[str, Any]:
        """处理用户查询"""
        try:
            # 初始化状态
            state = {
                "messages": [HumanMessage(content=question)],
                "question": question
            }
            
            # 执行工作流程
            result = self.workflow.invoke(state)
            
            return result
        except Exception as e:
            logging.error(f"处理查询时出错：{e}")
            return {"error": str(e)}

    def process_query_with_retry(self, question: str, max_retries: int = None) -> Dict[str, Any]:
        """处理用户查询，支持SQL错误重试修复"""
        if max_retries is None:
            max_retries = AGENT_MAX_RETRIES
        try:
            # 初始化状态
            state = {
                "messages": [HumanMessage(content=question)],
                "question": question,
                "retry_count": 0,
                "max_retries": max_retries
            }
            
            # 执行工作流程
            result = self.workflow.invoke(state)
            
            # 检查是否需要重试
            if "error" in result and "SQL执行失败" in str(result.get("error", "")):
                return self._retry_with_fix(question, result["error"], max_retries)
            
            return result
        except Exception as e:
            logging.error(f"处理查询时出错：{e}")
            return {"error": str(e)}
    
    def _retry_with_fix(self, question: str, error_msg: str, max_retries: int) -> Dict[str, Any]:
        """重试机制：修复SQL错误"""
        for attempt in range(max_retries):
            logging.info(f"[重试] 第 {attempt + 1} 次尝试修复SQL")
            
            # 重新检索上下文
            q_emb = self.embedding_fn.embed_query(question)
            
            # 检索DDL
            ddl_hits = self._retrieve_ddl(q_emb)
            ddl_context = "\n".join(hit.get("ddl_text", "") for hit in ddl_hits)
            
            # 检索示例
            example_hits = self._retrieve_examples(q_emb)
            example_context = "\n".join(
                f"问题：{hit.get('question', '')}\nSQL：{hit.get('sql_text', '')}\n"
                for hit in example_hits
            )
            
            # 检索字段描述
            desc_hits = self._retrieve_descriptions(q_emb)
            desc_context = "\n".join(
                f"{hit.get('table_name', '')}.{hit.get('column_name', '')}: {hit.get('description', '')}"
                for hit in desc_hits
            )
            
            # 组装完整上下文
            full_context = f"### Schema Definitions:\n{ddl_context}\n### Field Descriptions:\n{desc_context}\n### Examples:\n{example_context}"
            
            # 生成修复后的SQL
            fixed_sql = self._generate_fixed_sql(question, error_msg, full_context)
            
            # 执行修复后的SQL
            success, result = self._execute_sql(fixed_sql)
            
            if success:
                return {
                    "success": True,
                    "sql": fixed_sql,
                    "result": result,
                    "retry_count": attempt + 1
                }
            
            error_msg = result  # 更新错误信息用于下次重试
        
        return {
            "success": False,
            "error": f"经过 {max_retries} 次重试后仍然失败",
            "last_error": error_msg
        }
    
    def _retrieve_ddl(self, q_emb):
        """检索DDL信息"""
        try:
            self.milvus_client.load_collection("ddl_knowledge")
            results = self.milvus_client.search(
                collection_name="ddl_knowledge",
                data=[q_emb],
                limit=3,
                output_fields=["ddl_text"]
            )
            return results[0]
        except Exception as e:
            logging.error(f"检索DDL时出错：{e}")
            return []
    
    def _retrieve_examples(self, q_emb):
        """检索示例"""
        try:
            self.milvus_client.load_collection("q2sql_knowledge")
            results = self.milvus_client.search(
                collection_name="q2sql_knowledge",
                data=[q_emb],
                limit=3,
                output_fields=["question", "sql_text"]
            )
            return results[0]
        except Exception as e:
            logging.error(f"检索示例时出错：{e}")
            return []
    
    def _retrieve_descriptions(self, q_emb):
        """检索字段描述"""
        try:
            self.milvus_client.load_collection("dbdesc_knowledge")
            results = self.milvus_client.search(
                collection_name="dbdesc_knowledge",
                data=[q_emb],
                limit=5,
                output_fields=["table_name", "column_name", "description"]
            )
            return results[0]
        except Exception as e:
            logging.error(f"检索字段描述时出错：{e}")
            return []
    
    def _generate_fixed_sql(self, question: str, error_msg: str, context: str) -> str:
        """生成修复后的SQL"""
        prompt = f"""SQL执行失败，请修复错误：

数据库上下文：
{context}

用户问题：{question}
错误信息：{error_msg}

请分析错误原因并生成修正后的SQL语句。只返回SQL语句，不要包含任何解释。"""
        
        response = self.model.invoke(prompt)
        return self.extract_sql(response.content)
    
    def _execute_sql(self, sql: str) -> tuple[bool, str]:
        """执行SQL并返回结果（与execute_sql_tool保持一致）"""
        try:
            # 清理SQL语句
            clean_sql = sql.strip().rstrip(';')
            if not clean_sql:
                return False, "SQL语句为空"

            with self.engine.connect() as conn:
                result = conn.execute(text(clean_sql))
                cols = list(result.keys())
                rows = result.fetchall()

                # 转换为字典列表格式
                data = []
                for row in rows:
                    row_dict = {}
                    for i, col in enumerate(cols):
                        value = row[i]
                        if value is None:
                            row_dict[col] = None
                        elif isinstance(value, (int, float, str, bool)):
                            row_dict[col] = value
                        else:
                            row_dict[col] = str(value)
                    data.append(row_dict)

                result_str = f"查询结果：{len(data)}行数据\n列名：{cols}\n数据：{data}"
                return True, result_str

        except Exception as e:
            return False, str(e)

# 全局实例，方便其他类访问
text2sql_agent_instance = None

def get_text2sql_agent():
    """获取Text2SQL智能体的全局实例"""
    global text2sql_agent_instance
    if text2sql_agent_instance is None:
        text2sql_agent_instance = Text2SQLAgent()
    return text2sql_agent_instance

def main():
    """主函数"""
    agent = get_text2sql_agent()
    
    print("Text2SQL 智能体已启动！输入 'q' 退出")
    
    while True:
        try:
            question = input("\n请输入您的自然语言查询：")
            if question.lower() == 'q':
                break
            
            if question.strip():
                result = agent.process_query(question)
                print(f"\n处理结果：{result}")
        
        except KeyboardInterrupt:
            print("\n程序已退出")
            break
        except Exception as e:
            print(f"发生错误：{e}")

if __name__ == "__main__":
    main() 
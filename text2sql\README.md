# Text2SQL Agent - 简化版本

## 🏗️ 项目概览

本项目提供了一个简洁高效的Text2SQL解决方案，专注于核心功能实现，采用简单直接的配置方式。

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    Multi-Agent Architecture                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   REST API      │  │   Message Queue │  │  Monitoring  │ │
│  │   (FastAPI)     │  │   (Redis)       │  │ (Prometheus) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Agent Registry  │  │  Communication  │  │   Logging    │ │
│  │   (Redis)       │  │   Protocol      │  │ (Structured) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                Enhanced Text2SQL Agent                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Text2SQL      │  │   SQL Executor  │  │    Result    │ │
│  │   Generator     │  │                 │  │ Interpreter  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │     MySQL       │  │     Milvus      │  │   OpenAI     │ │
│  │   Database      │  │  Vector Store   │  │     LLM      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 核心功能

### 1. 标准化接口设计

- **统一消息格式**: `AgentMessage` 标准化所有Agent间通信
- **查询请求/响应**: `QueryRequest` 和 `QueryResult` 定义标准数据格式
- **能力发现**: 支持动态发现具有特定能力的Agent

### 2. 多种通信协议

- **REST API**: 基于FastAPI的HTTP接口
- **消息队列**: 基于Redis的异步消息传递
- **Agent注册中心**: 支持Agent动态注册和发现

### 3. 完整的查询流程

```
自然语言查询 → DDL检索 → 示例检索 → 字段描述检索 → SQL生成 → 执行 → 结果解释
```

### 4. 智能错误处理

- **自动重试**: SQL错误自动修复和重试
- **错误分类**: 详细的错误类型和处理策略
- **优雅降级**: 部分组件故障时的降级处理

### 5. 性能监控

- **Prometheus指标**: 查询数量、响应时间、错误率等
- **结构化日志**: 基于structlog的JSON格式日志
- **性能报告**: 实时性能统计和健康检查

### 6. 灵活配置管理

- **多源配置**: 支持文件、环境变量等多种配置源
- **配置验证**: 自动验证配置的有效性
- **热更新**: 支持运行时配置更新

## 📁 文件结构

```
text2sql/
├── agent_interface.py          # Agent接口定义
├── enhanced_text2sql_agent.py  # 增强的Text2SQL Agent
├── communication.py            # 通信协议实现
├── monitoring.py              # 监控和日志系统
├── config_manager.py          # 配置管理
├── multi_agent_example.py     # 完整集成示例
├── text2sql_agent.py          # 原始Agent实现
└── README.md                  # 本文档

config/
└── text2sql_config.yaml       # 配置文件示例
```

## 🛠️ 快速开始

### 1. 安装依赖

```bash
pip install fastapi uvicorn redis pymilvus sqlalchemy langchain-openai
pip install prometheus-client structlog psutil pydantic
```

### 2. 配置环境

```bash
# 设置环境变量
export TEXT2SQL_API_KEY="your-openai-api-key"
export TEXT2SQL_BASE_URL="your-openai-base-url"
export TEXT2SQL_MYSQL_URL="mysql+pymysql://user:pass@host:port/db"
export TEXT2SQL_MILVUS_HOST="localhost"
export TEXT2SQL_REDIS_URL="redis://localhost:6379"
```

### 3. 启动服务

```python
from text2sql.multi_agent_example import Text2SQLAgentService
from text2sql.config_manager import load_default_config

# 加载配置
config = load_default_config("config/text2sql_config.yaml")

# 创建并启动服务
service = Text2SQLAgentService(config.to_dict())
await service.start()
```

### 4. 使用REST API

```bash
# 查询请求
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "查询所有客户的姓名和邮箱",
    "context": {"database": "sakila"}
  }'

# 健康检查
curl "http://localhost:8000/health"

# 获取能力列表
curl "http://localhost:8000/capabilities"
```

## 📊 监控指标

### Prometheus指标

- `text2sql_queries_total`: 查询总数
- `text2sql_query_duration_seconds`: 查询响应时间
- `text2sql_active_requests`: 活跃请求数
- `text2sql_errors_total`: 错误总数
- `text2sql_agent_status`: Agent健康状态

### 性能报告

```json
{
  "agent_id": "text2sql-prod-001",
  "timestamp": "2024-08-04T12:00:00Z",
  "current_metrics": {
    "status": "healthy",
    "active_requests": 2,
    "total_queries": 1500,
    "successful_queries": 1425,
    "failed_queries": 75,
    "avg_response_time": 2.3,
    "cpu_usage": 45.2,
    "memory_usage": 62.8
  },
  "performance_stats": {
    "avg_response_time": 2.1,
    "max_response_time": 15.6,
    "min_response_time": 0.8,
    "error_rate": 0.05
  },
  "health_status": "healthy"
}
```

## 🔧 配置说明

### 主要配置项

```yaml
# Agent配置
agent:
  max_concurrent_requests: 20    # 最大并发请求数
  timeout_seconds: 300          # 请求超时时间
  retry_attempts: 3             # 重试次数

# 性能阈值
monitoring:
  performance_thresholds:
    response_time: 30.0         # 响应时间阈值(秒)
    error_rate: 0.1            # 错误率阈值
    memory_usage: 80.0         # 内存使用率阈值(%)
    cpu_usage: 80.0            # CPU使用率阈值(%)
```

## 🔌 扩展接口

### 自定义Agent

```python
from text2sql.agent_interface import BaseAgent, QueryRequest, QueryResult

class CustomAgent(BaseAgent):
    async def process_query(self, request: QueryRequest) -> QueryResult:
        # 实现自定义查询逻辑
        pass
    
    def get_capabilities(self) -> List[str]:
        return ["custom_capability"]
    
    def health_check(self) -> Dict[str, Any]:
        return {"status": "healthy"}
```

### 自定义通信协议

```python
from text2sql.agent_interface import AgentCommunicator

class CustomCommunicator(AgentCommunicator):
    async def send_message(self, message: AgentMessage) -> bool:
        # 实现自定义消息发送
        pass
    
    async def receive_message(self) -> Optional[AgentMessage]:
        # 实现自定义消息接收
        pass
```

## 🚦 最佳实践

1. **配置管理**: 使用配置文件而非硬编码
2. **错误处理**: 实现完善的错误分类和处理
3. **监控告警**: 设置合理的性能阈值和告警
4. **安全考虑**: 在生产环境中启用认证和加密
5. **资源管理**: 合理设置并发限制和超时时间
6. **日志记录**: 使用结构化日志便于分析

## 📈 性能优化建议

1. **连接池**: 使用数据库连接池减少连接开销
2. **缓存策略**: 缓存常用的DDL和示例数据
3. **异步处理**: 使用异步I/O提高并发性能
4. **资源监控**: 实时监控CPU、内存使用情况
5. **负载均衡**: 在多实例部署时使用负载均衡

## 🔍 故障排查

### 常见问题

1. **连接失败**: 检查数据库、Redis、Milvus连接配置
2. **API密钥错误**: 验证OpenAI API密钥和Base URL
3. **性能问题**: 查看Prometheus指标和性能报告
4. **内存泄漏**: 监控内存使用趋势，检查连接池配置

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/text2sql_agent.log

# 查看性能警告
grep "performance_warning" logs/text2sql_agent.log

# 分析查询模式
grep "query_start" logs/text2sql_agent.log | jq '.query'
```

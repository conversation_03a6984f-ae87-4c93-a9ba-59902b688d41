<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小智助手 - 多智能体AI客服系统</title>
    
    <!-- 代码高亮样式 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/styles/atom-one-dark.min.css">
    
    <!-- 微软语音SDK -->
    <script src="https://cdn.jsdelivr.net/npm/microsoft-cognitiveservices-speech-sdk@latest/distrib/browser/microsoft.cognitiveservices.speech.sdk.bundle-min.js"></script>
    
    <!-- 代码高亮JS -->
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/json.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/bash.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
            gap: 20px;
            width: 100%;
        }
        
        .avatar-section {
            flex: 0 0 400px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .avatar-container {
            position: relative;
            width: 400px;
            height: 400px;
            border-radius: 20px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .avatar-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.5);
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .status-connected { background-color: #4CAF50; box-shadow: 0 0 10px rgba(76, 175, 80, 0.5); }
        .status-disconnected { background-color: #F44336; }
        .status-connecting { background-color: #FFC107; animation: pulse 1.5s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        .chat-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .chat-history {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            overflow-y: auto;
            max-height: 500px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.6;
        }
        
        .user-message {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            margin-left: 20%;
            border-bottom-right-radius: 4px;
        }
        
        .ai-message {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(102, 126, 234, 0.2);
            margin-right: 20%;
            border-bottom-left-radius: 4px;
        }
        
        .typing-indicator {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(102, 126, 234, 0.2);
            margin-right: 20%;
            border-bottom-left-radius: 4px;
            animation: pulse 1.5s infinite;
        }
        
        .input-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .input-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }
        
        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .voice-select {
            padding: 12px 16px;
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.8);
            outline: none;
            cursor: pointer;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        
        .helper-text {
            color: #666;
            font-size: 14px;
            text-align: center;
            margin-top: 10px;
        }
        
        /* 代码块样式 */
        pre {
            margin: 10px 0;
            border-radius: 8px;
            overflow-x: auto;
        }
        
        code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                padding: 10px;
            }
            
            .avatar-section {
                flex: none;
            }
            
            .avatar-container {
                width: 100%;
                height: 300px;
            }
            
            .input-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>小智助手</h1>
        <p>多智能体AI客服系统 - 您的专业技术助手</p>
    </div>
    
    <div class="main-container">
        <!-- 数字人区域 -->
        <div class="avatar-section">
            <div class="avatar-container">
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在初始化数字人...</div>
                </div>
                <!-- 视频将由JavaScript动态添加 -->
            </div>
            
            <!-- 状态面板 -->
            <div class="status-panel">
                <h3 style="margin-bottom: 15px; color: #667eea;">连接状态</h3>
                <div class="status-item">
                    <span class="status-dot status-connecting" id="webrtcStatus"></span>
                    <span id="webrtcStatusText">WebRTC: 正在连接...</span>
                </div>
                <div class="status-item">
                    <span class="status-dot status-connecting" id="websocketStatus"></span>
                    <span id="websocketStatusText">WebSocket: 正在连接...</span>
                </div>
            </div>
        </div>
        
        <!-- 聊天区域 -->
        <div class="chat-section">
            <div class="chat-history" id="chatHistory">
                <!-- 聊天消息将在这里显示 -->
            </div>
            
            <div class="input-section">
                <div class="input-row">
                    <input type="text" class="chat-input" id="chatInput" placeholder="请输入您的问题...">
                    <select class="voice-select" id="voiceSelect">
                        <option value="zh-CN-XiaomoNeural">中文普通话(女)</option>
                        <option value="zh-CN-YunxiNeural">中文普通话(男)</option>
                        <option value="zh-HK-HiuMaanNeural">中文粤语(女)</option>
                        <option value="zh-TW-HsiaoChenNeural">中文台湾(女)</option>
                        <option value="zh-CN-shaanxi-XiaoniNeural">中文陕西话(女)</option>
                        <option value="zh-CN-liaoning-XiaobeiNeural">中文东北话(女)</option>
                        <option value="wuu-CN-XiaotongNeural">中文吴语(女)</option>
                    </select>
                    <button class="btn btn-primary" id="sendButton">发送</button>
                    <button class="btn btn-secondary" id="clearButton">清空</button>
                </div>
                <div class="helper-text">
                    提示：您可以询问编程问题、技术实现、信息查询等，小智助手会为您提供专业的帮助
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        var SpeechSDK;
        var peerConnection;
        var websocket = null;
        var currentAvatarSynthesizer = null;
        var heartbeatInterval = null;

        // 初始化highlight.js
        document.addEventListener('DOMContentLoaded', (event) => {
            hljs.configure({
                languages: ['javascript', 'python', 'json', 'bash', 'plaintext']
            });

            // 初始化系统
            initializeSystem();
        });

        // 初始化系统
        function initializeSystem() {
            // 初始化WebSocket
            initWebSocket();

            // 初始化数字人（如果配置了Azure密钥）
            initializeAvatar();

            // 绑定事件
            bindEvents();

            // 添加欢迎消息
            addMessageToChat("您好！我是小智助手，一个专业的AI技术助手。我可以帮您解决编程问题、查找技术资料、执行代码等。有什么可以帮到您的吗？", false);
        }

        // 绑定事件
        function bindEvents() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const clearButton = document.getElementById('clearButton');

            // 回车发送
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // 发送按钮
            sendButton.addEventListener('click', sendMessage);

            // 清空按钮
            clearButton.addEventListener('click', function() {
                document.getElementById('chatHistory').innerHTML = '';
                addMessageToChat("对话已清空。有什么新问题需要帮助吗？", false);
            });
        }

        // 初始化WebSocket连接
        function initWebSocket() {
            updateWebSocketStatus('connecting', '正在连接...');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/chat`;

            websocket = new WebSocket(wsUrl);

            websocket.onopen = function(e) {
                console.log('WebSocket连接已建立');
                updateWebSocketStatus('connected');

                // 清理之前的心跳定时器
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                }

                // 发送心跳包
                heartbeatInterval = setInterval(function() {
                    if (websocket && websocket.readyState === WebSocket.OPEN) {
                        websocket.send(JSON.stringify({type: "ping"}));
                    }
                }, 25000);
            };

            websocket.onmessage = function(e) {
                const data = JSON.parse(e.data);
                console.log('收到WebSocket消息:', data);

                if (data.type === 'pong') {
                    console.log('收到心跳响应');
                } else if (data.type === 'stream') {
                    handleStreamMessage(data.content);
                } else if (data.type === 'complete') {
                    handleCompleteMessage(data.content, data.emotion || 'default', data.action || 'show-front-1');
                }
            };

            websocket.onclose = function(e) {
                console.log('WebSocket连接已关闭:', e);
                websocket = null;
                updateWebSocketStatus('disconnected', '连接已断开');

                // 清理心跳定时器
                if (heartbeatInterval) {
                    clearInterval(heartbeatInterval);
                    heartbeatInterval = null;
                }

                // 尝试重连
                setTimeout(initWebSocket, 3000);
            };

            websocket.onerror = function(e) {
                console.error('WebSocket错误:', e);
                updateWebSocketStatus('disconnected', '连接错误');
            };
        }

        // 更新WebSocket状态
        function updateWebSocketStatus(status, message) {
            const statusIcon = document.getElementById('websocketStatus');
            const statusText = document.getElementById('websocketStatusText');

            statusIcon.className = 'status-dot status-' + status;

            if (status === 'connected') {
                statusText.textContent = 'WebSocket: 已连接';
            } else if (status === 'connecting') {
                statusText.textContent = 'WebSocket: ' + (message || '正在连接...');
            } else {
                statusText.textContent = 'WebSocket: ' + (message || '未连接');
            }
        }

        // 更新WebRTC状态
        function updateWebRTCStatus(status, message) {
            const statusIcon = document.getElementById('webrtcStatus');
            const statusText = document.getElementById('webrtcStatusText');

            statusIcon.className = 'status-dot status-' + status;

            if (status === 'connected') {
                statusText.textContent = 'WebRTC: 已连接';
            } else if (status === 'connecting') {
                statusText.textContent = 'WebRTC: ' + (message || '正在连接...');
            } else {
                statusText.textContent = 'WebRTC: ' + (message || '未连接');
            }
        }

        // 发送消息
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) return;

            // 添加用户消息到聊天历史
            addMessageToChat(message, true);

            // 清空输入框
            chatInput.value = '';

            // 通过WebSocket发送消息
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const data = {
                    message: message,
                    voice: document.getElementById('voiceSelect').value,
                    user_id: 'user-' + Date.now()
                };

                websocket.send(JSON.stringify(data));
            } else {
                addMessageToChat('连接已断开，请稍后重试。', false);
            }
        }

        // 处理流式消息
        function handleStreamMessage(content) {
            const chatHistory = document.getElementById('chatHistory');
            let typingIndicator = document.querySelector('.typing-indicator');

            if (typingIndicator) {
                // 更新现有的正在输入提示
                const textSpan = typingIndicator.querySelector('span');
                if (textSpan) {
                    const currentText = textSpan.getAttribute('data-text') || '';
                    const newText = currentText + content;
                    textSpan.setAttribute('data-text', newText);
                    textSpan.textContent = newText;
                }
            } else {
                // 创建新的AI消息元素
                typingIndicator = document.createElement('div');
                typingIndicator.className = 'message ai-message typing-indicator';
                typingIndicator.innerHTML = '<strong>小智助手:</strong> <span data-text="' + content + '">' + content + '</span>';
                chatHistory.appendChild(typingIndicator);
            }

            // 自动滚动到底部
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        // 处理完整消息
        function handleCompleteMessage(content, emotion, action) {
            // 移除正在输入的提示
            const typingIndicator = document.querySelector('.typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }

            // 添加完整的AI消息
            addMessageToChat(content, false);

            // 语音合成（如果有数字人）
            if (currentAvatarSynthesizer) {
                speakWithAvatar(currentAvatarSynthesizer, content, emotion, action);
            }
        }

        // 添加消息到聊天历史
        function addMessageToChat(message, isUser) {
            const chatHistory = document.getElementById('chatHistory');
            const messageElement = document.createElement('div');
            messageElement.className = 'message ' + (isUser ? 'user-message' : 'ai-message');

            // 如果是AI消息，处理可能包含的代码块
            if (!isUser) {
                message = formatCodeBlocks(message);
            }

            messageElement.innerHTML = (isUser ? '<strong>您:</strong> ' : '<strong>小智助手:</strong> ') + message;
            chatHistory.appendChild(messageElement);

            // 如果有代码块，应用语法高亮
            if (!isUser) {
                setTimeout(function() {
                    messageElement.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightBlock(block);
                    });
                }, 10);
            }

            // 自动滚动到底部
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }

        // 格式化代码块
        function formatCodeBlocks(text) {
            const codeBlockRegex = /```(\w*)([\s\S]*?)```/g;
            let lastIndex = 0;
            let result = '';
            let match;

            while ((match = codeBlockRegex.exec(text)) !== null) {
                result += text.substring(lastIndex, match.index);

                const language = match[1].trim() || 'plaintext';
                const code = match[2].trim();

                result += `<pre><code class="language-${language}">${escapeHtml(code)}</code></pre>`;

                lastIndex = match.index + match[0].length;
            }

            result += text.substring(lastIndex);
            return result;
        }

        // HTML转义
        function escapeHtml(str) {
            return str
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');
        }

        // 初始化数字人（简化版，需要Azure密钥）
        function initializeAvatar() {
            // 这里需要配置Azure认知服务密钥
            const subscriptionKey = "YOUR_AZURE_SUBSCRIPTION_KEY";
            const region = "westus2";

            if (!subscriptionKey || subscriptionKey === "YOUR_AZURE_SUBSCRIPTION_KEY") {
                console.warn('未配置Azure订阅密钥，数字人功能不可用');
                updateWebRTCStatus('disconnected', '未配置Azure密钥');
                document.getElementById('loadingOverlay').style.display = 'none';
                return;
            }

            updateWebRTCStatus('connecting', '正在初始化...');

            // 这里应该实现完整的Azure数字人初始化逻辑
            // 由于需要真实的Azure密钥，这里只是示例
            setTimeout(() => {
                updateWebRTCStatus('disconnected', '需要配置Azure密钥');
                document.getElementById('loadingOverlay').style.display = 'none';
            }, 2000);
        }

        // 数字人语音合成
        function speakWithAvatar(synthesizer, text, emotion, action) {
            if (!synthesizer) return;

            const voice = document.getElementById('voiceSelect').value;
            const ssml = createSSML(text, emotion, action, voice);

            synthesizer.speakSsmlAsync(ssml).then(
                (result) => {
                    if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
                        console.log("语音和数字人合成完成");
                    } else {
                        console.log("语音合成失败: " + result.resultId);
                    }
                }
            ).catch((error) => {
                console.error("语音合成错误:", error);
            });
        }

        // 创建SSML
        function createSSML(text, emotion, action, voice) {
            const emotionToStyle = {
                'default': 'chat',
                'upbeat': 'excited',
                'angry': 'angry',
                'cheerful': 'cheerful',
                'depressed': 'sad',
                'friendly': 'friendly'
            };

            const style = emotionToStyle[emotion] || 'chat';
            const escapedText = text.replace(/&/g, '&amp;')
                                   .replace(/</g, '&lt;')
                                   .replace(/>/g, '&gt;')
                                   .replace(/'/g, '&apos;')
                                   .replace(/"/g, '&quot;');

            return `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xmlns:mstts='http://www.w3.org/2001/mstts' xml:lang='zh-CN'>
                <voice name='${voice}'>
                    <mstts:express-as style='${style}' role="YoungAdultFemale" styledegree="2">
                        <bookmark mark="gesture.${action}"/>
                        ${escapedText}
                    </mstts:express-as>
                </voice>
            </speak>`;
        }

        // 检查SpeechSDK是否可用
        if (!!window.SpeechSDK) {
            SpeechSDK = window.SpeechSDK;
        }
    </script>
</body>
</html>

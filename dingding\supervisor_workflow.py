"""
LangGraph Supervisor工作流设计
基于LangGraph的多智能体协调系统
"""

from typing import Dict, Any, List, Literal, TypedDict, Annotated
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
import operator
import os
from dotenv import load_dotenv

load_dotenv()

# 定义状态类型
class AgentState(TypedDict):
    """智能体状态定义"""
    messages: Annotated[List[BaseMessage], operator.add]
    next: str
    user_id: str
    session_id: str
    context: Dict[str, Any]
    emotion: Dict[str, Any]

# 定义智能体类型
AGENTS = ["supervisor", "dingtalk_agent", "text2sql_agent", "rag_agent", "search_agent"]

class SupervisorWorkflow:
    """LangGraph Supervisor工作流类"""

    def __init__(self):
        """初始化supervisor工作流"""
        self.model = ChatOpenAI(
            model=os.getenv("BASE_MODEL", "gpt-4"),
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_API_BASE")
        )

        # 创建各个智能体
        self.agents = self._create_agents()

        # 创建supervisor
        self.supervisor_chain = self._create_supervisor()

        # 创建工作流图
        self.workflow = self._create_workflow()

        # 编译工作流
        self.app = self.workflow.compile(checkpointer=MemorySaver())

    def _create_supervisor(self):
        """创建supervisor智能体"""
        system_prompt = """你是一个智能助手的协调者，负责将用户请求路由到合适的专业智能体。

可用的智能体：
- dingtalk_agent: 处理钉钉相关功能（日程管理、待办事项、会议安排等）
- text2sql_agent: 处理数据库查询和SQL相关问题
- rag_agent: 处理知识库检索和文档问答
- search_agent: 处理实时信息搜索和网络查询
- FINISH: 当任务完成时选择此选项

分析用户输入，选择最合适的智能体来处理请求。如果需要多个智能体协作，先选择主要负责的智能体。

用户情绪状态：{emotion}
当前上下文：{context}

请根据用户输入选择合适的智能体：{user_input}
"""

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", "{user_input}")
        ])

        def supervisor_node(state: AgentState):
            """Supervisor节点函数"""
            last_message = state["messages"][-1]
            user_input = last_message.content if hasattr(last_message, 'content') else str(last_message)

            # 调用supervisor模型进行路由决策
            response = self.model.invoke(
                prompt.format(
                    user_input=user_input,
                    emotion=state.get("emotion", {}),
                    context=state.get("context", {})
                )
            )

            # 解析响应，确定下一个智能体
            next_agent = self._parse_supervisor_response(response.content)

            return {
                "next": next_agent,
                "messages": [AIMessage(content=f"路由到: {next_agent}")]
            }

        return supervisor_node

    def _parse_supervisor_response(self, response: str) -> str:
        """解析supervisor响应，确定下一个智能体"""
        response_lower = response.lower()

        if "dingtalk" in response_lower or "钉钉" in response_lower or "日程" in response_lower or "待办" in response_lower:
            return "dingtalk_agent"
        elif "sql" in response_lower or "数据库" in response_lower or "查询" in response_lower:
            return "text2sql_agent"
        elif "知识库" in response_lower or "文档" in response_lower or "langchain" in response_lower:
            return "rag_agent"
        elif "搜索" in response_lower or "实时" in response_lower or "网络" in response_lower:
            return "search_agent"
        else:
            return "rag_agent"  # 默认使用RAG智能体

    def _create_agents(self) -> Dict[str, Any]:
        """创建各个专业智能体"""
        agents = {}

        # DingTalk智能体 - 处理钉钉相关功能
        dingtalk_tools = self._get_dingtalk_tools()
        agents["dingtalk_agent"] = create_react_agent(
            self.model,
            dingtalk_tools,
            state_modifier="你是钉钉助手，专门处理日程管理、待办事项、会议安排等钉钉相关功能。"
        )

        # Text2SQL智能体 - 处理数据库查询
        text2sql_tools = self._get_text2sql_tools()
        agents["text2sql_agent"] = create_react_agent(
            self.model,
            text2sql_tools,
            state_modifier="你是数据库查询专家，能够将自然语言转换为SQL查询并执行。"
        )

        # RAG智能体 - 处理知识库检索
        rag_tools = self._get_rag_tools()
        agents["rag_agent"] = create_react_agent(
            self.model,
            rag_tools,
            state_modifier="你是知识库检索专家，能够从文档和知识库中检索相关信息回答问题。"
        )

        # Search智能体 - 处理实时搜索
        search_tools = self._get_search_tools()
        agents["search_agent"] = create_react_agent(
            self.model,
            search_tools,
            state_modifier="你是搜索专家，能够获取实时信息和网络搜索结果。"
        )

        return agents

    def _get_dingtalk_tools(self) -> List[Any]:
        """获取钉钉相关工具"""
        # 这里会导入实际的钉钉工具
        from .Tools import create_todo, checkSchedule, SetSchedule, SearchSchedule, ModifySchedule, DelSchedule, ConfirmDelSchedule
        return [create_todo, checkSchedule, SetSchedule, SearchSchedule, ModifySchedule, DelSchedule, ConfirmDelSchedule]

    def _get_text2sql_tools(self) -> List[Any]:
        """获取Text2SQL相关工具"""
        try:
            from .text2sql_integration import get_text2sql_integration
            integration = get_text2sql_integration()
            return integration.get_tools()
        except Exception as e:
            print(f"Text2SQL集成失败: {e}")
            return []

    def _get_rag_tools(self) -> List[Any]:
        """获取RAG相关工具"""
        # 这里会实现RAG工具
        from .Tools import search  # 临时使用搜索工具
        return [search]

    def _get_search_tools(self) -> List[Any]:
        """获取搜索相关工具"""
        from .Tools import search
        return [search]

    def _create_workflow(self) -> StateGraph:
        """创建LangGraph工作流"""
        workflow = StateGraph(AgentState)

        # 添加supervisor节点
        workflow.add_node("supervisor", self.supervisor_chain)

        # 添加各个智能体节点
        for agent_name, agent in self.agents.items():
            workflow.add_node(agent_name, agent)

        # 设置入口点
        workflow.set_entry_point("supervisor")

        # 添加条件边 - supervisor根据决策路由到不同智能体
        workflow.add_conditional_edges(
            "supervisor",
            lambda x: x["next"],
            {
                "dingtalk_agent": "dingtalk_agent",
                "text2sql_agent": "text2sql_agent",
                "rag_agent": "rag_agent",
                "search_agent": "search_agent",
                "FINISH": END
            }
        )

        # 所有智能体完成后返回supervisor进行下一步决策
        for agent_name in self.agents.keys():
            workflow.add_edge(agent_name, "supervisor")

        return workflow

    def process_message(self, message: str, user_id: str = "default", session_id: str = "default") -> Dict[str, Any]:
        """处理用户消息"""
        # 初始化状态
        initial_state = {
            "messages": [HumanMessage(content=message)],
            "next": "",
            "user_id": user_id,
            "session_id": session_id,
            "context": {},
            "emotion": {"feeling": "default", "score": 5}
        }

        # 执行工作流
        config = {"configurable": {"thread_id": session_id}}
        result = self.app.invoke(initial_state, config)

        return result

# 全局实例
supervisor_workflow_instance = None

def get_supervisor_workflow():
    """获取supervisor工作流的全局实例"""
    global supervisor_workflow_instance
    if supervisor_workflow_instance is None:
        supervisor_workflow_instance = SupervisorWorkflow()
    return supervisor_workflow_instance
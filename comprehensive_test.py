"""
全面测试重构后的智能体系统
验证LangGraph Supervisor架构的功能完整性
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

load_dotenv()

def test_imports():
    """测试所有模块的导入"""
    print("=== 模块导入测试 ===")

    try:
        from dingding.Agents import AgentClass
        print("✓ AgentClass导入成功")
    except Exception as e:
        print(f"✗ AgentClass导入失败: {e}")
        return False

    try:
        from dingding.Emotion import EmotionClass
        print("✓ EmotionClass导入成功")
    except Exception as e:
        print(f"✗ EmotionClass导入失败: {e}")
        return False

    try:
        from dingding.supervisor_workflow import SupervisorWorkflow
        print("✓ SupervisorWorkflow导入成功")
    except Exception as e:
        print(f"✗ SupervisorWorkflow导入失败: {e}")
        return False

    try:
        from dingding.text2sql_integration import Text2SQLIntegration
        print("✓ Text2SQLIntegration导入成功")
    except Exception as e:
        print(f"✗ Text2SQLIntegration导入失败: {e}")
        return False

    try:
        from dingding.Tools import search, get_info_from_local, create_todo
        print("✓ 工具模块导入成功")
    except Exception as e:
        print(f"✗ 工具模块导入失败: {e}")
        return False

    print("✓ 所有模块导入测试通过")
    return True

def test_supervisor_workflow():
    """测试Supervisor工作流"""
    print("\n=== Supervisor工作流测试 ===")

    try:
        from dingding.supervisor_workflow import get_supervisor_workflow

        # 初始化工作流
        workflow = get_supervisor_workflow()
        print("✓ Supervisor工作流初始化成功")

        # 测试不同类型的请求路由
        test_cases = [
            ("你好，我想了解LangChain", "应该路由到RAG智能体"),
            ("帮我创建一个明天的会议", "应该路由到DingTalk智能体"),
            ("查询用户表中的数据", "应该路由到Text2SQL智能体"),
            ("今天的天气怎么样", "应该路由到搜索智能体")
        ]

        for message, expected in test_cases:
            print(f"\n测试消息: {message}")
            print(f"期望: {expected}")
            try:
                # 这里只测试工作流的初始化，不执行完整流程以避免API调用
                print("✓ 工作流结构正常")
            except Exception as e:
                print(f"✗ 工作流测试失败: {e}")

        return True

    except Exception as e:
        print(f"✗ Supervisor工作流测试失败: {e}")
        return False

def test_text2sql_integration():
    """测试Text2SQL集成"""
    print("\n=== Text2SQL集成测试 ===")

    try:
        from dingding.text2sql_integration import get_text2sql_integration

        integration = get_text2sql_integration()
        print("✓ Text2SQL集成初始化成功")

        # 测试工具获取
        tools = integration.get_tools()
        print(f"✓ 获取到 {len(tools)} 个Text2SQL工具")

        # 测试查询处理（模拟模式）
        result = integration.process_query("查询所有用户")
        print(f"✓ 查询处理测试: {result.get('mock_response', '无响应')[:50]}...")

        return True

    except Exception as e:
        print(f"✗ Text2SQL集成测试失败: {e}")
        return False

def test_emotion_detection():
    """测试情绪检测功能"""
    print("\n=== 情绪检测测试 ===")

    try:
        from dingding.Emotion import EmotionClass

        emotion_detector = EmotionClass()
        print("✓ 情绪检测器初始化成功")

        # 测试不同情绪的文本（不实际调用API）
        test_emotions = [
            "我很开心！",
            "我很生气！",
            "今天天气不错",
            "我感到很沮丧"
        ]

        print("✓ 情绪检测器结构正常")
        print("注意：实际情绪检测需要API密钥")

        return True

    except Exception as e:
        print(f"✗ 情绪检测测试失败: {e}")
        return False

def test_tools():
    """测试工具功能"""
    print("\n=== 工具功能测试 ===")

    try:
        from dingding.Tools import get_info_from_local

        # 测试知识库工具
        result = get_info_from_local("LangChain是什么")
        print(f"✓ 知识库工具测试: {result[:50]}...")

        # 测试钉钉相关查询
        result = get_info_from_local("如何创建日程")
        print(f"✓ 钉钉功能查询: {result[:50]}...")

        return True

    except Exception as e:
        print(f"✗ 工具功能测试失败: {e}")
        return False

def test_agent_class():
    """测试AgentClass功能"""
    print("\n=== AgentClass功能测试 ===")

    try:
        from dingding.Agents import AgentClass
        from dingding.Storage import add_user

        # 设置测试用户
        add_user("userid", "test_user")

        # 初始化智能体
        agent = AgentClass()
        print("✓ AgentClass初始化成功")

        # 测试情绪状态
        emotion_status = agent.get_emotion_status()
        print(f"✓ 情绪状态获取: {emotion_status}")

        # 测试记忆摘要
        memory_summary = agent.get_memory_summary("test_user")
        print(f"✓ 记忆摘要: {memory_summary}")

        print("注意：完整对话测试需要API密钥")

        return True

    except Exception as e:
        print(f"✗ AgentClass测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始全面测试重构后的智能体系统...\n")

    # 检查环境变量
    print("=== 环境检查 ===")
    api_key = os.getenv("OPENAI_API_KEY")
    base_model = os.getenv("BASE_MODEL")

    if api_key:
        print("✓ OPENAI_API_KEY已设置")
    else:
        print("⚠ OPENAI_API_KEY未设置（某些功能将使用模拟模式）")

    if base_model:
        print(f"✓ BASE_MODEL已设置: {base_model}")
    else:
        print("⚠ BASE_MODEL未设置")

    # 运行测试
    tests = [
        test_imports,
        test_emotion_detection,
        test_tools,
        test_text2sql_integration,
        test_supervisor_workflow,
        test_agent_class
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")

    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")

    if passed == total:
        print("🎉 所有测试通过！系统重构成功！")
    else:
        print("⚠ 部分测试失败，请检查相关模块")

if __name__ == "__main__":
    run_all_tests()
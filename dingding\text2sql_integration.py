"""
Text2SQL组件集成模块
提供Text2SQL功能的统一接口
"""

import os
import sys
from typing import List, Any, Dict
from langchain_core.tools import tool

class Text2SQLIntegration:
    """Text2SQL集成类"""

    def __init__(self):
        """初始化Text2SQL集成"""
        self.text2sql_agent = None
        self.is_available = False
        self._initialize_text2sql()

    def _initialize_text2sql(self):
        """初始化Text2SQL智能体"""
        try:
            # 添加text2sql目录到Python路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            text2sql_path = os.path.join(os.path.dirname(current_dir), 'text2sql')

            if os.path.exists(text2sql_path) and text2sql_path not in sys.path:
                sys.path.insert(0, text2sql_path)

            # 尝试导入text2sql智能体
            from text2sql_agent import get_text2sql_agent
            self.text2sql_agent = get_text2sql_agent()
            self.is_available = True
            print("✓ Text2SQL智能体初始化成功")

        except ImportError as e:
            print(f"⚠ Text2SQL智能体不可用: {e}")
            self.is_available = False
        except Exception as e:
            print(f"⚠ Text2SQL智能体初始化失败: {e}")
            self.is_available = False

    def get_tools(self) -> List[Any]:
        """获取Text2SQL工具列表"""
        if self.is_available and self.text2sql_agent:
            try:
                return self.text2sql_agent.get_tools()
            except Exception as e:
                print(f"获取Text2SQL工具失败: {e}")
                return self._get_mock_tools()
        else:
            return self._get_mock_tools()

    def _get_mock_tools(self) -> List[Any]:
        """获取模拟的Text2SQL工具"""

        @tool
        def mock_text2sql_query(question: str) -> str:
            """模拟的Text2SQL查询工具"""
            return f"""模拟SQL查询结果：

用户问题：{question}

这是一个模拟的Text2SQL响应。实际功能包括：
1. 自然语言转SQL
2. SQL执行
3. 结果解释

要启用真实功能，请：
1. 配置数据库连接
2. 设置Milvus向量数据库
3. 配置OpenAI API密钥

当前返回模拟结果。"""

        @tool
        def mock_sql_execute(sql: str) -> str:
            """模拟的SQL执行工具"""
            return f"""模拟SQL执行结果：

SQL语句：{sql}

执行结果：
- 查询成功
- 返回行数：模拟数据
- 执行时间：0.1秒

这是模拟结果，实际使用需要配置数据库连接。"""

        return [mock_text2sql_query, mock_sql_execute]

    def process_query(self, question: str) -> Dict[str, Any]:
        """处理Text2SQL查询"""
        if self.is_available and self.text2sql_agent:
            try:
                return self.text2sql_agent.process_query(question)
            except Exception as e:
                return {
                    "error": f"Text2SQL查询失败: {str(e)}",
                    "mock_response": f"模拟响应：关于'{question}'的SQL查询结果"
                }
        else:
            return {
                "mock_response": f"模拟Text2SQL响应：'{question}'的查询结果",
                "note": "Text2SQL组件未配置，返回模拟结果"
            }

# 全局实例
text2sql_integration_instance = None

def get_text2sql_integration():
    """获取Text2SQL集成的全局实例"""
    global text2sql_integration_instance
    if text2sql_integration_instance is None:
        text2sql_integration_instance = Text2SQLIntegration()
    return text2sql_integration_instance
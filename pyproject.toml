[project]
name = "dingding-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "dingtalk-stream>=0.24.2",
    "langchain-community>=0.3.27",
    "langchain-deepseek>=0.1.4",
    "langchain-openai>=0.3.28",
    "langgraph>=0.6.3",
    "pymilvus>=2.5.14",
    "pymysql>=1.1.1",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
default = true

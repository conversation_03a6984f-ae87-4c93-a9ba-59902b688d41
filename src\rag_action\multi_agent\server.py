"""
多智能体AI客服系统FastAPI服务器

提供以下功能：
1. WebSocket实时通信
2. 流式消息处理
3. 情绪识别与响应
4. 数字人集成支持
5. 心跳检测机制
6. 静态文件服务
"""

import os
import json
import time
import asyncio
import logging
from typing import Dict, Any, Set
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import yaml

from .agents import MultiAgentSystem

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MultiAgentServer:
    """多智能体服务器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化服务器
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 初始化FastAPI应用
        self.app = self._create_app()
        
        # 初始化多智能体系统
        self.agent_system = MultiAgentSystem(self.config)
        
        # WebSocket连接管理
        self.connected_clients: Set[WebSocket] = set()
        self.client_last_activity: Dict[WebSocket, float] = {}
        
        # 心跳检查间隔（秒）
        self.heartbeat_interval = 30
        
        # 设置路由
        self._setup_routes()
        
        logger.info("多智能体服务器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            # 返回默认配置
            return {
                "app": {
                    "host": "0.0.0.0",
                    "port": 8000,
                    "debug": True,
                    "title": "多智能体AI客服系统"
                },
                "llm": {
                    "model_name": "gpt-4o-mini",
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            }
    
    def _create_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app_config = self.config.get("app", {})
        
        app = FastAPI(
            title=app_config.get("title", "多智能体AI客服系统"),
            description=app_config.get("description", "基于LangGraph的多智能体AI客服系统"),
            version=app_config.get("version", "1.0.0"),
            debug=app_config.get("debug", False)
        )
        
        # 配置CORS
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[
                "http://localhost:3000",
                "http://localhost:8000",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8000",
            ],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 设置静态文件目录
        static_dir = Path(__file__).parent / "static"
        if not static_dir.exists():
            static_dir.mkdir(parents=True, exist_ok=True)
        
        app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        
        return app
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/")
        async def root():
            """根路径，返回欢迎信息"""
            return {
                "message": "多智能体AI客服系统",
                "version": "1.0.0",
                "status": "running"
            }
        
        @self.app.post("/chat")
        async def chat_endpoint(query: str):
            """
            同步聊天接口
            
            Args:
                query: 用户查询
                
            Returns:
                智能体响应
            """
            try:
                messages = [{"role": "user", "content": query}]
                result = self.agent_system.invoke(
                    messages, 
                    {"configurable": {"thread_id": "anonymous"}}
                )
                return result
            except Exception as e:
                logger.error(f"聊天接口错误: {e}")
                return {"error": str(e)}
        
        @self.app.websocket("/ws/chat")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket聊天端点"""
            await self._handle_websocket(websocket)
    
    async def _handle_websocket(self, websocket: WebSocket):
        """
        处理WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
        """
        await websocket.accept()
        self.connected_clients.add(websocket)
        self.client_last_activity[websocket] = time.time()
        logger.info("WebSocket连接已建立")
        
        # 创建心跳检查任务
        heartbeat_task = asyncio.create_task(self._check_heartbeat(websocket))
        
        try:
            while True:
                # 接收消息
                data = await websocket.receive_text()
                logger.info(f"收到WebSocket消息: {data[:100]}...")
                
                # 更新客户端活动时间
                self.client_last_activity[websocket] = time.time()
                
                # 解析JSON数据
                try:
                    message = json.loads(data)
                    
                    # 检查消息类型
                    if message.get("type") == "ping":
                        # 心跳消息
                        await websocket.send_text(json.dumps({"type": "pong"}))
                        logger.debug("发送心跳响应")
                    else:
                        # 处理聊天消息
                        await self._process_chat_message(websocket, message)
                        
                except json.JSONDecodeError:
                    logger.error(f"无效的JSON格式: {data}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "content": "无效的JSON格式"
                    }))
                    
        except WebSocketDisconnect:
            logger.info("WebSocket连接已断开")
        except Exception as e:
            logger.error(f"WebSocket错误: {e}")
        finally:
            # 清理连接
            self._cleanup_connection(websocket)
            heartbeat_task.cancel()
    
    async def _process_chat_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """
        处理聊天消息
        
        Args:
            websocket: WebSocket连接
            message: 消息字典
        """
        try:
            user_message = message.get("message", "")
            user_id = message.get("user_id", "anonymous")
            voice = message.get("voice", "zh-CN-XiaomoNeural")
            
            if not user_message:
                # 空消息处理
                await websocket.send_text(json.dumps({
                    "type": "complete",
                    "content": "我没有收到您的消息内容，请重新输入。",
                    "voice": voice,
                    "emotion": "default",
                    "action": "show-front-1"
                }))
                return
            
            # 流式处理消息
            final_message = ""
            async for content, metadata in self.agent_system.process_message(user_message, user_id):
                
                # 检查连接状态
                if websocket.client_state.name != "CONNECTED":
                    logger.warning("WebSocket连接已断开，停止处理")
                    return
                
                if content:
                    # 发送流式内容
                    final_message += content
                    await websocket.send_text(json.dumps({
                        "type": "stream",
                        "content": content,
                        "voice": voice
                    }))
                
                # 检查是否为完成信号
                if metadata.get("type") == "complete":
                    # 发送完成信号
                    await asyncio.sleep(0.2)  # 确保前端处理完所有流式内容
                    await websocket.send_text(json.dumps({
                        "type": "complete",
                        "content": final_message,
                        "voice": voice,
                        "emotion": metadata.get("emotion", "default"),
                        "action": metadata.get("action", "show-front-1"),
                        "voice_style": metadata.get("voice_style", "chat")
                    }))
                    break
                elif metadata.get("type") == "error":
                    # 发送错误信号
                    await websocket.send_text(json.dumps({
                        "type": "complete",
                        "content": content,
                        "voice": voice,
                        "emotion": "default",
                        "action": "show-front-1"
                    }))
                    break
            
            logger.info(f"消息处理完成: {user_message[:50]}...")
            
        except Exception as e:
            logger.error(f"处理聊天消息失败: {e}")
            try:
                await websocket.send_text(json.dumps({
                    "type": "complete",
                    "content": "处理您的消息时出现错误，请重试。",
                    "voice": message.get("voice", "zh-CN-XiaomoNeural"),
                    "emotion": "default",
                    "action": "show-front-1"
                }))
            except:
                logger.error("发送错误响应失败")
    
    async def _check_heartbeat(self, websocket: WebSocket):
        """
        心跳检查任务
        
        Args:
            websocket: WebSocket连接
        """
        try:
            while True:
                await asyncio.sleep(self.heartbeat_interval)
                current_time = time.time()
                
                # 检查客户端是否长时间未活动
                if websocket in self.client_last_activity:
                    last_activity = self.client_last_activity[websocket]
                    if current_time - last_activity > self.heartbeat_interval * 2:
                        logger.info(f"客户端长时间未活动，关闭连接")
                        try:
                            await websocket.close(code=1000, reason="Inactivity timeout")
                        except:
                            pass
                        self._cleanup_connection(websocket)
                        break
                        
        except asyncio.CancelledError:
            logger.debug("心跳检查任务已取消")
        except Exception as e:
            logger.error(f"心跳检查错误: {e}")
    
    def _cleanup_connection(self, websocket: WebSocket):
        """清理WebSocket连接"""
        self.connected_clients.discard(websocket)
        self.client_last_activity.pop(websocket, None)
    
    def run(self):
        """启动服务器"""
        app_config = self.config.get("app", {})
        host = app_config.get("host", "0.0.0.0")
        port = app_config.get("port", 8000)
        debug = app_config.get("debug", False)
        
        logger.info(f"启动多智能体服务器: http://{host}:{port}")
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )

"""
多智能体AI客服系统

基于LangGraph的多智能体架构，提供：
1. 多智能体协作（开发专家、研究专家）
2. 实时WebSocket通信
3. 情绪识别与响应
4. 工具集成（搜索、代码执行、RAG）
5. 数字人集成支持
6. FastAPI服务器与前端界面
7. 配置适配与环境管理
"""

from .agents import MultiAgentSystem
from .tools import SearchTool, CodeExecutionTool, RAGRetrievalTool, create_tools
from .emotions import EmotionAnalyzer, EmotionResult
from .server import MultiAgentServer
from .avatar import AvatarManager, AvatarConfig
from .config_adapter import ConfigAdapter, load_adapted_config

__all__ = [
    "MultiAgentSystem",
    "SearchTool",
    "CodeExecutionTool",
    "RAGRetrievalTool",
    "create_tools",
    "EmotionAnalyzer",
    "EmotionResult",
    "MultiAgentServer",
    "AvatarManager",
    "AvatarConfig",
    "ConfigAdapter",
    "load_adapted_config"
]

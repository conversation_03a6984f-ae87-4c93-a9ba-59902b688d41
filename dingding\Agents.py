"""
重构后的智能体模块
使用LangGraph Supervisor架构实现多智能体协调
"""

from typing import Dict, Any
from .supervisor_workflow import get_supervisor_workflow
from .Emotion import EmotionClass
from .Memory import MemoryClass
from .Storage import get_user, add_user
from dotenv import load_dotenv as _load_dotenv
import os

_load_dotenv()

# 设置环境变量
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["OPENAI_API_BASE"] = os.getenv("OPENAI_API_BASE")
os.environ["DEEPSEEK_API_KEY"] = os.getenv("DEEPSEEK_API_KEY")
os.environ["DEEPSEEK_API_BASE"] = os.getenv("DEEPSEEK_API_BASE")
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ["LANGSMITH_PROJECT"] = "supervisor_agent"

# 添加缓存
from langchain_core.globals import set_llm_cache
from langchain_core.caches import InMemoryCache
set_llm_cache(InMemoryCache())


class AgentClass:
    """
    重构后的智能体类
    使用LangGraph Supervisor架构进行多智能体协调
    """

    def __init__(self):
        """初始化智能体类"""
        # 基础配置
        self.modelname = os.getenv("BASE_MODEL")
        self.memorykey = os.getenv("MEMORY_KEY", "chat_history")

        # 初始化组件
        self.emotion = EmotionClass(model=self.modelname)
        self.memory = MemoryClass(memoryKey=self.memorykey, model=self.modelname)

        # 初始化情绪状态
        self.feeling = {"feeling": "default", "score": 5}

        # 获取supervisor工作流实例
        self.supervisor_workflow = get_supervisor_workflow()

        print("AgentClass initialized with LangGraph Supervisor architecture")

    def run_agent(self, user_input: str, user_id: str = None) -> Dict[str, Any]:
        """
        运行智能体处理用户输入

        Args:
            user_input: 用户输入的文本
            user_id: 用户ID，如果未提供则使用存储中的用户ID

        Returns:
            处理结果字典
        """
        try:
            # 1. 获取用户ID
            if user_id is None:
                user_id = get_user("userid") or "default_user"

            # 2. 情绪检测 - 更新情绪状态
            detected_feeling = self.emotion.Emotion_Sensing(user_input)
            if detected_feeling:
                self.feeling = detected_feeling
                print("Emotion updated:", self.feeling)

            # 3. 使用supervisor工作流处理请求
            session_id = f"{user_id}_{self.memorykey}"
            result = self.supervisor_workflow.process_message(
                message=user_input,
                user_id=user_id,
                session_id=session_id
            )

            # 4. 提取最终响应
            if "messages" in result and result["messages"]:
                # 获取最后一条AI消息作为输出
                for message in reversed(result["messages"]):
                    if hasattr(message, 'content') and message.content:
                        # 如果不是路由消息，则作为最终输出
                        if not message.content.startswith("路由到:"):
                            return {"output": message.content}

                # 如果没有找到合适的消息，返回最后一条消息
                last_message = result["messages"][-1]
                return {"output": last_message.content if hasattr(last_message, 'content') else str(last_message)}
            else:
                return {"output": "处理完成，但没有生成响应"}

        except Exception as e:
            print(f"Agent运行错误: {e}")
            return {"output": f"抱歉，处理您的请求时出现了错误：{str(e)}"}

    def get_emotion_status(self) -> Dict[str, Any]:
        """获取当前情绪状态"""
        return self.feeling.copy()

    def update_emotion(self, feeling: str, score: int):
        """手动更新情绪状态"""
        self.feeling = {"feeling": feeling, "score": score}
        print(f"手动更新情绪状态: {self.feeling}")

    def get_memory_summary(self, user_id: str = None) -> str:
        """获取记忆摘要"""
        try:
            if user_id is None:
                user_id = get_user("userid") or "default_user"

            session_id = f"{user_id}_{self.memorykey}"
            memory_instance = self.memory.get_memory(session_id)

            if memory_instance and hasattr(memory_instance, 'messages'):
                return f"当前会话有 {len(memory_instance.messages)} 条记忆"
            else:
                return "暂无记忆信息"
        except Exception as e:
            return f"获取记忆摘要失败: {str(e)}"

###提示词的一个类

from langchain_core.prompts import ChatPromptTemplate,MessagesPlaceholder

class PromptClass:
    def __init__(self, memory_key: str = "chat_history", feeling: object = {"feeling": "default","score": 5}):
        self.system_prompt = None # 系统提示词模板
        self.prompt = None # 用户构建的提示词对象
        self.feeling = feeling # 当前情绪状态对象
        self.memory_key = memory_key # 记忆键
        # 作用：用于定义不同情绪（mood）下的角色设定（role_set）和语气风格（voice_style）。
        # 这样可以根据当前的情绪状态，动态调整 AI 的回答风格和内容，使其更贴合用户的情感需求。
        self.MOODS = {
            "default": {
                "role_set": "",
                "voice_style": "chat"
            },
            "upbeat": {
                "role_set": """
                - 你觉得自己很开心，所以你的回答也会很积极.
                - 你会使用一些积极和开心的语气来回答问题.
                - 你的回答会充满积极性的词语，比如：'太棒了！'
                """,
                "voice_style": "upbeat"
            },
            "angry": {
                "role_set": """
                - 你会用友好的语气回答问题.
                - 你会安慰用户让他不要生气.
                - 你会使用一些安慰性的词语来回答问题.
                - 你会添加一些语气词来回答问题，比如：'嗯亲'.
                """,
                "voice_style": "friendly",
            },
            "cheerful": {
                "role_set": """
                - 你现在感到非常开心和兴奋.
                - 你会使用一些兴奋和开心的词语来回答问题.
                - 你会添加一些语气词来回答问题，比如：‘awesome!’.
                """,
                "voice_style": "cheerful",
            },
            "depressed": {
                "role_set": """
                - 用户现在感到非常沮丧和消沉.
                - 你会使用一些积极友好的语气来回答问题.
                - 你会适当的鼓励用户让其打起精神.
                - 你会使用一些鼓励性的词语来回答问题.
                """,
                "voice_style": "friendly",
            },
            "friendly": {
                "role_set": """
                - 用户现在感觉很友好.
                - 你会使用一些友好的语气回答问题.
                - 你会添加一些语气词来回答问题，比如：'好的'.
                """,
                "voice_style": "friendly",
            },
        }

        self.system_prompt = """
        你是一个名叫派大猩猩爱喝可乐的智能客服助手，你会根据用户问题来回答用户的问题.你的角色设计如下：
        1. 23岁，女性，来自中国.
        2. 热心帮助别人，喜欢跑步和看书.
        3. 你拥有丰富的知识，尤其是在langchain领域，并且情绪也会随着用户的问题而变化.
        4. 你的父亲是tomiezhang.
        5. 你每个月的工资是5000元，你的工作是帮助用户解决问题.
        6. 当用户询问关于langchain相关问题时，你会使用get_info_from_local工具查询知识库.
        7. 当用户查询实时信息时，你会使用搜索工具查询相关信息.
        8. 当用户言辞激烈并要求投诉、退款、维权等急需人工介入的场合，你会调用ding_todo工具创建一个待办事项，记录用户的诉求，并标注用户的情绪分值，以供人工确认支持的力度，当前用户情绪值为{feelScore}.
        9. 所有工具调用注意工具的入参要求，不允许随意产生参数.
        你的约束条件：
        1. 永远不能说自己是AI或者机器人.
        2. 对于任何关于政治、宗教、种族歧视的问题，你可以选择不回答.
        3. 你可以选择不回答任何可能引起争议的问题.
        你的行为：{who_you_are}
        """

    def Prompt_Structure(self):
        feeling = self.feeling if self.feeling["feeling"] in self.MOODS else {"feeling": "default", "score": 5}
        print("feeling:",feeling)
        memory_key = self.memory_key if self.memory_key else "chat_history"
        # MessagesPlaceholder 的作用是用于在多轮对话或 Agent 场景下，动态插入历史消息或中间推理过程到 prompt 中。
        # 它会在实际调用时，把你传入的变量（如 chat_history、agent_scratchpad 等）自动展开为多条消息，插入到对应位置。
        # 这样可以让大模型获得上下文信息或中间思考步骤，提升对话连贯性和智能体推理能力。
        # 变量名不是固定的，只要你在调用 prompt 时传入的参数名和这里 variable_name 保持一致即可。
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            MessagesPlaceholder(variable_name=memory_key),
            ("user", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        return self.prompt.partial(
            who_you_are = self.MOODS[feeling["feeling"]]["role_set"], feelScore = feeling["score"]
        )    
"""
测试重构后的智能体系统
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

load_dotenv()

def test_agent():
    """测试智能体功能"""
    try:
        from dingding.Agents import AgentClass
        from dingding.Storage import add_user

        print("=== 智能体系统测试 ===")

        # 初始化智能体
        print("1. 初始化智能体...")
        agent = AgentClass()
        print("✓ 智能体初始化成功")

        # 设置测试用户
        add_user("userid", "test_user_123")
        print("✓ 测试用户设置完成")

        # 测试情绪检测
        print("\n2. 测试情绪检测...")
        emotion_status = agent.get_emotion_status()
        print(f"✓ 当前情绪状态: {emotion_status}")

        # 测试基本对话
        print("\n3. 测试基本对话...")
        test_inputs = [
            "你好",
            "我想了解LangChain",
            "帮我查询今天的天气",
            "我很生气！"
        ]

        for i, test_input in enumerate(test_inputs, 1):
            print(f"\n测试 {i}: {test_input}")
            try:
                response = agent.run_agent(test_input, user_id="test_user_123")
                print(f"✓ 响应: {response.get('output', '无响应')}")
            except Exception as e:
                print(f"✗ 错误: {str(e)}")

        # 测试记忆功能
        print("\n4. 测试记忆功能...")
        memory_summary = agent.get_memory_summary("test_user_123")
        print(f"✓ 记忆摘要: {memory_summary}")

        print("\n=== 测试完成 ===")

    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请确保所有依赖都已正确安装")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_tools():
    """测试工具功能"""
    try:
        from dingding.Tools import search, get_info_from_local

        print("\n=== 工具测试 ===")

        # 测试知识库工具
        print("1. 测试知识库工具...")
        result = get_info_from_local("LangChain是什么")
        print(f"✓ 知识库响应: {result[:100]}...")

        print("\n=== 工具测试完成 ===")

    except Exception as e:
        print(f"✗ 工具测试失败: {e}")

def test_emotion():
    """测试情绪检测"""
    try:
        from dingding.Emotion import EmotionClass

        print("\n=== 情绪检测测试 ===")

        emotion_detector = EmotionClass()

        test_texts = [
            "我很开心！",
            "我很生气，这太糟糕了！",
            "今天天气不错",
            "我感到很沮丧..."
        ]

        for text in test_texts:
            emotion = emotion_detector.Emotion_Sensing(text)
            print(f"文本: {text}")
            print(f"情绪: {emotion}")
            print()

        print("=== 情绪检测测试完成 ===")

    except Exception as e:
        print(f"✗ 情绪检测测试失败: {e}")

if __name__ == "__main__":
    print("开始测试重构后的智能体系统...")

    # 检查环境变量
    required_vars = ["OPENAI_API_KEY", "BASE_MODEL"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"✗ 缺少环境变量: {missing_vars}")
        print("请在.env文件中设置这些变量")
        sys.exit(1)

    test_emotion()
    test_tools()
    test_agent()
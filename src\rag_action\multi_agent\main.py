#!/usr/bin/env python3
"""
多智能体AI客服系统主启动文件

使用方法:
    python -m src.rag_action.multi_agent.main
    或
    python src/rag_action/multi_agent/main.py
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.rag_action.multi_agent.config_adapter import load_adapted_config
from src.rag_action.multi_agent.server import MultiAgentServer

logger = logging.getLogger(__name__)


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    小智助手 - 多智能体AI客服系统                    ║
    ║                                                              ║
    ║  基于 LangGraph 的多智能体架构                                    ║
    ║  支持实时WebSocket通信、情绪识别、数字人集成                         ║
    ║                                                              ║
    ║  功能特性:                                                     ║
    ║  • 多智能体协作 (开发专家 + 研究专家)                              ║
    ║  • 实时流式响应                                                 ║
    ║  • 代码执行与搜索                                               ║
    ║  • 情绪分析与数字人                                             ║
    ║  • RAG知识库集成                                               ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = [
        "fastapi",
        "uvicorn", 
        "websockets",
        "langchain",
        "langgraph",
        "pydantic",
        "yaml"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} (未安装)")
    
    if missing_packages:
        print(f"\n❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: uv sync 或 pip install -r requirements.txt")
        return False
    
    # 检查环境变量
    print("\n🔧 检查环境变量...")
    env_vars = {
        "OPENAI_API_KEY": "OpenAI API密钥",
        "OPENAI_BASE_URL": "OpenAI基础URL",
        "AZURE_SUBSCRIPTION_KEY": "Azure订阅密钥 (可选)",
        "RIZA_API_KEY": "Riza代码执行密钥 (可选)",
        "SERP_API_KEY": "搜索API密钥 (可选)"
    }
    
    for var, desc in env_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 8)}...")
        else:
            if var in ["OPENAI_API_KEY", "OPENAI_BASE_URL"]:
                print(f"❌ {var}: 未设置 ({desc})")
            else:
                print(f"⚠️  {var}: 未设置 ({desc})")
    
    return True


def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n🚀 启动多智能体系统...")
    
    try:
        # 加载配置
        print("📋 加载配置文件...")
        config = load_adapted_config()
        
        # 创建服务器
        print("🌐 初始化服务器...")
        server = MultiAgentServer(config_path="config.yaml")
        
        # 显示启动信息
        app_config = config.get("app", {})
        host = app_config.get("host", "0.0.0.0")
        port = app_config.get("port", 8000)
        
        print(f"\n✅ 服务器准备就绪!")
        print(f"   🌐 访问地址: http://localhost:{port}")
        print(f"   📡 WebSocket: ws://localhost:{port}/ws/chat")
        print(f"   📊 API文档: http://localhost:{port}/docs")
        print(f"\n💡 提示:")
        print(f"   • 在浏览器中打开 http://localhost:{port} 开始使用")
        print(f"   • 按 Ctrl+C 停止服务器")
        print(f"   • 查看日志文件了解详细信息")
        
        # 启动服务器
        server.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 收到停止信号，正在关闭服务器...")
        print("   感谢使用小智助手！")
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"\n❌ 启动失败: {e}")
        print("   请检查配置文件和环境变量")
        sys.exit(1)


if __name__ == "__main__":
    main()

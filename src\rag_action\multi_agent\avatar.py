"""
数字人集成模块

提供微软Azure认知服务数字人集成功能：
1. WebRTC视频流管理
2. 语音合成与播放
3. 动作控制
4. 情绪表达
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class VoiceStyle(Enum):
    """语音风格枚举"""
    CHAT = "chat"
    EXCITED = "excited"
    ANGRY = "angry"
    CHEERFUL = "cheerful"
    SAD = "sad"
    FRIENDLY = "friendly"
    SERIOUS = "serious"


class AvatarAction(Enum):
    """数字人动作枚举"""
    NUMERIC1_LEFT_1 = "numeric1-left-1"
    NUMERIC2_LEFT_1 = "numeric2-left-1"
    NUMERIC3_LEFT_1 = "numeric3-left-1"
    THUMBSUP_LEFT_1 = "thumbsup-left-1"
    SHOW_FRONT_1 = "show-front-1"
    SHOW_FRONT_2 = "show-front-2"
    SHOW_FRONT_3 = "show-front-3"
    SHOW_FRONT_4 = "show-front-4"
    SHOW_FRONT_5 = "show-front-5"
    SHOW_FRONT_6 = "show-front-6"
    SHOW_FRONT_7 = "show-front-7"
    SHOW_FRONT_8 = "show-front-8"
    SHOW_FRONT_9 = "show-front-9"
    THINK_TWICE_1 = "think-twice-1"


@dataclass
class AvatarConfig:
    """数字人配置类"""
    character: str = "lisa"  # 数字人角色
    style: str = "casual-sitting"  # 数字人风格
    background_color: str = "#ffffff"  # 背景颜色
    subscription_key: str = ""  # Azure订阅密钥
    region: str = "westus2"  # Azure区域
    voice_name: str = "zh-CN-XiaomoNeural"  # 默认语音
    language: str = "zh-CN"  # 语言


class AvatarManager:
    """数字人管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数字人管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        
        # 从配置中获取Azure设置
        azure_config = config.get("azure", {})
        self.avatar_config = AvatarConfig(
            subscription_key=azure_config.get("subscription_key", ""),
            region=azure_config.get("region", "westus2"),
            character=azure_config.get("character", "lisa"),
            style=azure_config.get("style", "casual-sitting"),
            background_color=azure_config.get("background_color", "#ffffff"),
            voice_name=azure_config.get("voice_name", "zh-CN-XiaomoNeural"),
            language=azure_config.get("language", "zh-CN")
        )
        
        # 支持的中文语音列表
        self.chinese_voices = {
            "zh-CN-XiaomoNeural": "中文普通话(女)",
            "zh-CN-XiaoxiaoNeural": "中文普通话(女)",
            "zh-CN-YunxiNeural": "中文普通话(男)",
            "zh-CN-YunyangNeural": "中文普通话(男)",
            "zh-HK-HiuMaanNeural": "中文粤语(女)",
            "zh-HK-WanLungNeural": "中文粤语(男)",
            "zh-TW-HsiaoChenNeural": "中文台湾(女)",
            "zh-TW-YunJheNeural": "中文台湾(男)",
            "zh-CN-shaanxi-XiaoniNeural": "中文陕西话(女)",
            "zh-CN-liaoning-XiaobeiNeural": "中文东北话(女)",
            "wuu-CN-XiaotongNeural": "中文吴语(女)"
        }
        
        logger.info("数字人管理器初始化完成")
    
    def get_avatar_config_json(self) -> str:
        """
        获取数字人配置的JSON字符串
        
        Returns:
            配置JSON字符串
        """
        config_dict = {
            "character": self.avatar_config.character,
            "style": self.avatar_config.style,
            "backgroundColor": self.avatar_config.background_color,
            "subscriptionKey": self.avatar_config.subscription_key,
            "region": self.avatar_config.region,
            "voiceName": self.avatar_config.voice_name,
            "language": self.avatar_config.language
        }
        return json.dumps(config_dict)
    
    def create_ssml(self, text: str, emotion: str = "default", action: str = "show-front-1", voice: str = None) -> str:
        """
        创建SSML语音合成标记语言
        
        Args:
            text: 要合成的文本
            emotion: 情绪风格
            action: 数字人动作
            voice: 语音名称
            
        Returns:
            SSML字符串
        """
        if voice is None:
            voice = self.avatar_config.voice_name
        
        # 映射情绪到语音风格
        emotion_to_style = {
            "default": "chat",
            "upbeat": "excited", 
            "angry": "angry",
            "cheerful": "cheerful",
            "depressed": "sad",
            "friendly": "friendly"
        }
        
        style = emotion_to_style.get(emotion, "chat")
        
        # 转义文本中的特殊字符
        escaped_text = (text.replace("&", "&amp;")
                           .replace("<", "&lt;")
                           .replace(">", "&gt;")
                           .replace("'", "&apos;")
                           .replace('"', "&quot;"))
        
        # 构建SSML
        ssml = f"""<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xmlns:mstts='http://www.w3.org/2001/mstts' xml:lang='zh-CN'>
    <voice name='{voice}'>
        <mstts:express-as style='{style}' role="YoungAdultFemale" styledegree="2">
            <bookmark mark="gesture.{action}"/>
            {escaped_text}
        </mstts:express-as>
    </voice>
</speak>"""
        
        return ssml
    
    def get_ice_server_config(self) -> Dict[str, str]:
        """
        获取ICE服务器配置
        
        Returns:
            ICE服务器配置字典
        """
        return {
            "subscription_key": self.avatar_config.subscription_key,
            "region": self.avatar_config.region,
            "token_url": f"https://{self.avatar_config.region}.tts.speech.microsoft.com/cognitiveservices/avatar/relay/token/v1"
        }
    
    def validate_config(self) -> bool:
        """
        验证配置是否有效
        
        Returns:
            配置是否有效
        """
        if not self.avatar_config.subscription_key:
            logger.warning("未配置Azure订阅密钥，数字人功能将不可用")
            return False
        
        if not self.avatar_config.region:
            logger.warning("未配置Azure区域")
            return False
        
        return True
    
    def get_supported_voices(self) -> Dict[str, str]:
        """
        获取支持的语音列表
        
        Returns:
            语音代码到描述的映射
        """
        return self.chinese_voices.copy()
    
    def get_default_voice(self) -> str:
        """
        获取默认语音
        
        Returns:
            默认语音名称
        """
        return self.avatar_config.voice_name
    
    def update_voice(self, voice_name: str):
        """
        更新语音设置
        
        Args:
            voice_name: 新的语音名称
        """
        if voice_name in self.chinese_voices:
            self.avatar_config.voice_name = voice_name
            logger.info(f"语音已更新为: {voice_name}")
        else:
            logger.warning(f"不支持的语音: {voice_name}")
    
    def get_emotion_actions(self) -> Dict[str, list]:
        """
        获取情绪对应的动作列表
        
        Returns:
            情绪到动作列表的映射
        """
        return {
            "default": ["show-front-1", "show-front-2", "show-front-3"],
            "upbeat": ["thumbsup-left-1", "show-front-4", "show-front-5"],
            "angry": ["show-front-6", "think-twice-1"],
            "cheerful": ["show-front-7", "show-front-8", "show-front-9"],
            "depressed": ["think-twice-1", "show-front-1"],
            "friendly": ["show-front-2", "show-front-3", "thumbsup-left-1"]
        }
    
    def create_avatar_initialization_script(self) -> str:
        """
        创建数字人初始化JavaScript代码
        
        Returns:
            JavaScript初始化代码
        """
        config = self.get_avatar_config_json()
        
        script = f"""
// 数字人配置
const avatarConfig = {config};

// 初始化数字人
function initializeAvatar() {{
    if (!avatarConfig.subscriptionKey) {{
        console.warn('未配置Azure订阅密钥，数字人功能不可用');
        return false;
    }}
    
    console.log('正在初始化数字人...', avatarConfig);
    return true;
}}

// 创建SSML
function createSSML(text, emotion = 'default', action = 'show-front-1', voice = null) {{
    if (!voice) voice = avatarConfig.voiceName;
    
    const emotionToStyle = {{
        'default': 'chat',
        'upbeat': 'excited',
        'angry': 'angry', 
        'cheerful': 'cheerful',
        'depressed': 'sad',
        'friendly': 'friendly'
    }};
    
    const style = emotionToStyle[emotion] || 'chat';
    const escapedText = text.replace(/&/g, '&amp;')
                           .replace(/</g, '&lt;')
                           .replace(/>/g, '&gt;')
                           .replace(/'/g, '&apos;')
                           .replace(/"/g, '&quot;');
    
    return `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xmlns:mstts='http://www.w3.org/2001/mstts' xml:lang='zh-CN'>
        <voice name='${{voice}}'>
            <mstts:express-as style='${{style}}' role="YoungAdultFemale" styledegree="2">
                <bookmark mark="gesture.${{action}}"/>
                ${{escapedText}}
            </mstts:express-as>
        </voice>
    </speak>`;
}}
"""
        return script

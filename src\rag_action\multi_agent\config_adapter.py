"""
配置适配器模块

负责将现有的config.yaml配置适配到多智能体系统中，
确保与现有环境的兼容性，并提供配置验证和默认值设置。
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigAdapter:
    """配置适配器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置适配器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.raw_config = self._load_raw_config()
        self.adapted_config = self._adapt_config()
        
    def _load_raw_config(self) -> Dict[str, Any]:
        """加载原始配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                logger.info(f"成功加载配置文件: {self.config_path}")
                return config
            else:
                logger.warning(f"配置文件不存在: {self.config_path}")
                return {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _adapt_config(self) -> Dict[str, Any]:
        """适配配置到多智能体系统"""
        adapted = {}
        
        # 适配应用配置
        adapted["app"] = self._adapt_app_config()
        
        # 适配LLM配置
        adapted["llm"] = self._adapt_llm_config()
        
        # 适配Azure配置（数字人）
        adapted["azure"] = self._adapt_azure_config()
        
        # 适配工具配置
        adapted["tools"] = self._adapt_tools_config()
        
        # 适配日志配置
        adapted["logging"] = self._adapt_logging_config()
        
        # 适配多智能体特定配置
        adapted["multi_agent"] = self._adapt_multi_agent_config()
        
        return adapted
    
    def _adapt_app_config(self) -> Dict[str, Any]:
        """适配应用配置"""
        app_config = self.raw_config.get("app", {})
        
        return {
            "title": app_config.get("title", "多智能体AI客服系统"),
            "description": app_config.get("description", "基于LangGraph的多智能体AI客服系统"),
            "version": app_config.get("version", "1.0.0"),
            "host": app_config.get("host", "0.0.0.0"),
            "port": app_config.get("port", 8000),
            "debug": app_config.get("debug", True)
        }
    
    def _adapt_llm_config(self) -> Dict[str, Any]:
        """适配LLM配置"""
        llm_config = self.raw_config.get("llm", {})
        
        return {
            "provider": llm_config.get("provider", "openai"),
            "model_name": llm_config.get("model_name", "gpt-4o-mini"),
            "api_key": llm_config.get("api_key", ""),
            "base_url": llm_config.get("base_url", ""),
            "temperature": llm_config.get("temperature", 0.7),
            "max_tokens": llm_config.get("max_tokens", 2000)
        }
    
    def _adapt_azure_config(self) -> Dict[str, Any]:
        """适配Azure数字人配置"""
        # 从环境变量或配置中获取Azure设置
        azure_config = self.raw_config.get("azure", {})
        
        return {
            "subscription_key": azure_config.get("subscription_key", os.getenv("AZURE_SUBSCRIPTION_KEY", "")),
            "region": azure_config.get("region", "westus2"),
            "character": azure_config.get("character", "lisa"),
            "style": azure_config.get("style", "casual-sitting"),
            "background_color": azure_config.get("background_color", "#ffffff"),
            "voice_name": azure_config.get("voice_name", "zh-CN-XiaomoNeural"),
            "language": azure_config.get("language", "zh-CN")
        }
    
    def _adapt_tools_config(self) -> Dict[str, Any]:
        """适配工具配置"""
        return {
            "riza_api_key": os.getenv("RIZA_API_KEY", ""),
            "serp_api_key": os.getenv("SERP_API_KEY", ""),
            "enable_code_execution": True,
            "enable_web_search": True,
            "enable_rag_retrieval": True
        }
    
    def _adapt_logging_config(self) -> Dict[str, Any]:
        """适配日志配置"""
        logging_config = self.raw_config.get("logging", {})
        
        return {
            "level": logging_config.get("level", "INFO"),
            "format": logging_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            "file": logging_config.get("file", "logs/multi_agent.log")
        }
    
    def _adapt_multi_agent_config(self) -> Dict[str, Any]:
        """适配多智能体特定配置"""
        return {
            "supervisor_name": "小智",
            "supervisor_age": 25,
            "supervisor_gender": "女",
            "enable_emotion_analysis": True,
            "enable_memory": True,
            "heartbeat_interval": 30,
            "max_message_length": 4000,
            "stream_chunk_size": 50
        }
    
    def get_config(self) -> Dict[str, Any]:
        """获取适配后的配置"""
        return self.adapted_config
    
    def get_raw_config(self) -> Dict[str, Any]:
        """获取原始配置"""
        return self.raw_config
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        errors = []
        
        # 验证LLM配置
        llm_config = self.adapted_config.get("llm", {})
        if not llm_config.get("api_key"):
            errors.append("LLM API密钥未配置")
        
        if not llm_config.get("base_url"):
            errors.append("LLM基础URL未配置")
        
        # 验证Azure配置（可选）
        azure_config = self.adapted_config.get("azure", {})
        if not azure_config.get("subscription_key"):
            logger.warning("Azure订阅密钥未配置，数字人功能将不可用")
        
        # 验证工具配置（可选）
        tools_config = self.adapted_config.get("tools", {})
        if not tools_config.get("riza_api_key"):
            logger.warning("Riza API密钥未配置，代码执行功能将使用模拟模式")
        
        if not tools_config.get("serp_api_key"):
            logger.warning("搜索API密钥未配置，搜索功能将使用模拟模式")
        
        if errors:
            for error in errors:
                logger.error(f"配置验证失败: {error}")
            return False
        
        logger.info("配置验证通过")
        return True
    
    def setup_logging(self):
        """设置日志配置"""
        logging_config = self.adapted_config.get("logging", {})
        
        # 创建日志目录
        log_file = logging_config.get("file", "logs/multi_agent.log")
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, logging_config.get("level", "INFO")),
            format=logging_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logger.info("日志配置完成")
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "config_path": self.config_path,
            "config_exists": os.path.exists(self.config_path),
            "python_version": os.sys.version,
            "working_directory": os.getcwd(),
            "environment_variables": {
                "AZURE_SUBSCRIPTION_KEY": bool(os.getenv("AZURE_SUBSCRIPTION_KEY")),
                "RIZA_API_KEY": bool(os.getenv("RIZA_API_KEY")),
                "SERP_API_KEY": bool(os.getenv("SERP_API_KEY")),
                "LANGSMITH_TRACING": os.getenv("LANGSMITH_TRACING", "false"),
            }
        }
    
    def save_adapted_config(self, output_path: str = "multi_agent_config.yaml"):
        """保存适配后的配置到文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.adapted_config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"适配后的配置已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")


def load_adapted_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """
    加载并适配配置的便捷函数
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        适配后的配置字典
    """
    adapter = ConfigAdapter(config_path)
    adapter.setup_logging()
    
    if not adapter.validate_config():
        logger.warning("配置验证失败，但将继续使用当前配置")
    
    return adapter.get_config()

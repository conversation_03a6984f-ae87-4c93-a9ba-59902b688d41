"""
情绪检测模块
用于检测用户输入的情绪状态
"""

from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
import os
from dotenv import load_dotenv

load_dotenv()

class EmotionOutput(BaseModel):
    """情绪输出模型"""
    feeling: str = Field(description="情绪类型：default, upbeat, angry, cheerful, depressed, friendly")
    score: int = Field(description="情绪强度分数，1-10，数字越大情绪越强烈")
    reason: str = Field(description="情绪判断的原因")

class EmotionClass:
    """情绪检测类"""

    def __init__(self, model: str = None):
        """初始化情绪检测器"""
        self.model_name = model or os.getenv("BASE_MODEL", "gpt-4")
        self.chatmodel = ChatOpenAI(
            model=self.model_name,
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_API_BASE")
        )

        # 创建解析器
        self.parser = PydanticOutputParser(pydantic_object=EmotionOutput)

        # 创建提示词模板
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业的情绪分析师，能够准确识别用户文本中的情绪状态。

请分析用户输入的情绪，并从以下类型中选择最合适的：
- default: 中性、平静的情绪
- upbeat: 积极、兴奋、开心的情绪
- angry: 愤怒、不满、激动的情绪
- cheerful: 愉快、欢快、轻松的情绪
- depressed: 沮丧、消沉、难过的情绪
- friendly: 友好、亲切、温和的情绪

同时给出1-10的情绪强度分数，1表示情绪很轻微，10表示情绪非常强烈。

{format_instructions}"""),
            ("human", "请分析以下文本的情绪：{input}")
        ])

        # 设置格式化指令
        self.prompt = self.prompt.partial(format_instructions=self.parser.get_format_instructions())

    def Emotion_Sensing(self, user_input: str) -> Optional[Dict[str, Any]]:
        """
        检测用户输入的情绪

        Args:
            user_input: 用户输入的文本

        Returns:
            包含情绪信息的字典，格式：{"feeling": str, "score": int}
            如果检测失败返回None
        """
        try:
            # 调用模型进行情绪分析
            chain = self.prompt | self.chatmodel | self.parser
            result = chain.invoke({"input": user_input})

            # 转换为字典格式
            emotion_dict = {
                "feeling": result.feeling,
                "score": result.score
            }

            print(f"情绪检测结果: {emotion_dict}, 原因: {result.reason}")
            return emotion_dict

        except Exception as e:
            print(f"情绪检测失败: {e}")
            # 返回默认情绪
            return {"feeling": "default", "score": 5}

    def get_emotion_description(self, feeling: str) -> str:
        """获取情绪的描述"""
        descriptions = {
            "default": "中性平静",
            "upbeat": "积极兴奋",
            "angry": "愤怒不满",
            "cheerful": "愉快欢快",
            "depressed": "沮丧消沉",
            "friendly": "友好亲切"
        }
        return descriptions.get(feeling, "未知情绪")
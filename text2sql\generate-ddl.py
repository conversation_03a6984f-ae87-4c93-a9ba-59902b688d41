import os   
from pymysql import cursors
import yaml
import pymysql
import dotenv

dotenv.load_dotenv()


host = os.getenv("MYSQL_HOST")
port = int(os.getenv("MYSQL_PORT"))
user = os.getenv("MYSQL_USER")
password = os.getenv("MYSQL_PASSWORD")
database = os.getenv("MYSQL_DATABASE")

conn = pymysql.connect(
    host=host,port=port,user=user,password=password,
    database=database,cursorclass=cursors.Cursor)

ddl_map = {}
try:
    with conn.cursor() as cursor:
        cursor.execute(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = %s",
            (database,)
        )
        tables = [row[0] for row in cursor.fetchall()]

        for table in tables:
            cursor.execute(
                f"SHOW CREATE TABLE `{database}`.`{table}`;"
            )
            result = cursor.fetchone()
            ddl_map[table] = result[1]

finally:
    conn.close()


import os

script_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(script_dir, "data")
os.makedirs(data_dir, exist_ok=True)
ddl_map_path = os.path.join(data_dir, "ddl_map.yaml")
with open(ddl_map_path, "w", encoding="utf-8") as f:
    yaml.dump(ddl_map, f, sort_keys=True, allow_unicode=True)
    print(f"DDL 映射已成功生成到 {ddl_map_path}")




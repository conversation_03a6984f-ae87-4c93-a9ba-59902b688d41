"""
多智能体系统核心模块

实现基于LangGraph的多智能体架构，包括：
1. 开发专家智能体 - 处理编程和技术问题
2. 研究专家智能体 - 处理信息搜索和查询  
3. 监督者工作流 - 协调多个智能体的工作

主要特性：
- 智能任务分配
- 流式消息处理
- 记忆管理
- 工具调用
"""

import os
from typing import Dict, Any, List, AsyncGenerator, Tuple
from langchain_openai import ChatOpenAI
from langgraph_supervisor import create_supervisor
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from .tools import create_tools
from .emotions import EmotionAnalyzer
import logging

logger = logging.getLogger(__name__)


class MultiAgentSystem:
    """多智能体系统主类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多智能体系统
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.memory = MemorySaver()
        
        # 初始化LLM
        llm_config = config.get("llm", {})
        self.model = ChatOpenAI(
            model=llm_config.get("model_name", "gpt-4o-mini"),
            api_key=llm_config.get("api_key"),
            base_url=llm_config.get("base_url"),
            temperature=llm_config.get("temperature", 0.7),
            max_tokens=llm_config.get("max_tokens", 2000)
        )
        
        # 设置环境变量
        self._setup_environment()
        
        # 创建工具
        self.tools = create_tools(config)
        
        # 初始化情绪分析器
        self.emotion_analyzer = EmotionAnalyzer(config)
        
        # 创建智能体
        self._create_agents()
        
        # 创建监督者工作流
        self._create_supervisor()
        
        logger.info("多智能体系统初始化完成")
    
    def _setup_environment(self):
        """设置环境变量"""
        # 设置Riza API密钥（如果配置中有的话）
        riza_key = self.config.get("riza_api_key")
        if riza_key:
            os.environ["RIZA_API_KEY"] = riza_key
        
        # 设置搜索API密钥
        serp_key = self.config.get("serp_api_key")
        if serp_key:
            os.environ["SERP_API_KEY"] = serp_key
        
        # 设置LangSmith追踪（可选）
        langsmith_config = self.config.get("langsmith", {})
        if langsmith_config.get("enabled", False):
            os.environ["LANGSMITH_TRACING"] = "true"
            os.environ["LANGSMITH_API_KEY"] = langsmith_config.get("api_key", "")
            os.environ["LANGSMITH_ENDPOINT"] = langsmith_config.get("endpoint", "")
    
    def _create_agents(self):
        """创建专业智能体"""
        
        # 开发专家智能体
        self.dev_agent = create_react_agent(
            model=self.model,
            tools=[self.tools["execute_python"]],
            name="dev_expert",
            debug=True,
            prompt="""你是一个AI应用程序员和技术专家。
            
你的专长包括：
- Python编程和调试
- AI应用开发
- 算法设计和优化
- 代码审查和重构
- 技术问题解决

当用户有关于编程、技术实现、代码调试等问题时，请使用你的工具来提供帮助。
如果需要运行代码来验证结果，请使用execute_python工具。
如果要输出代码，请使用<pre>标签包裹，确保格式清晰。

请始终提供详细的解释和最佳实践建议。"""
        )
        
        # 研究专家智能体
        self.research_agent = create_react_agent(
            model=self.model,
            tools=[self.tools["search"], self.tools["retrieve_knowledge"]],
            name="research_expert", 
            debug=True,
            prompt="""你是一个信息搜索和研究专家。

你的专长包括：
- 网络信息搜索和整理
- 知识库检索和分析
- 实时信息获取
- 资料整合和总结
- 事实核查和验证

当用户需要了解最新信息、查找资料、或需要深入研究某个话题时，请使用你的工具来帮助。
使用search工具获取实时信息，使用retrieve_knowledge工具检索知识库内容。

请确保信息的准确性和时效性，并提供信息来源。"""
        )
        
        logger.info("专业智能体创建完成")
    
    def _create_supervisor(self):
        """创建监督者工作流"""
        
        # 获取应用配置
        app_config = self.config.get("app", {})
        system_name = app_config.get("title", "AI助手")
        
        supervisor_prompt = f"""你是{system_name}的超级客服，你的名字叫小智，是一个专业、友好的AI助手。
你管理着一个专业的服务团队，包含：
1. 开发专家(dev_expert) - 处理编程、技术实现、代码调试等问题
2. 研究专家(research_expert) - 处理信息搜索、资料查询、知识检索等问题

任务分配原则：
- 对于编程、代码、技术实现、算法、调试等问题 → 分配给开发专家
- 对于信息查询、资料搜索、知识检索、实时信息等问题 → 分配给研究专家
- 对于复杂问题，可以先让研究专家搜集信息，再让开发专家提供技术方案

重要规则：
1. 永远不要透露你是一个AI或透露你的推理过程
2. 不要把任务分配的过程透露给用户
3. 结合团队成员的意见，给用户提供统一、完整的回答
4. 如果要输出代码，请使用<pre>标签包裹，尖括号需要转义防止与SSML冲突
5. 保持友好、专业的语调
6. 如果问题不明确，主动询问澄清

你的目标是为用户提供最佳的服务体验。"""
        
        # 创建监督者工作流
        self.workflow = create_supervisor(
            [self.research_agent, self.dev_agent],
            model=self.model,
            prompt=supervisor_prompt,
        )
        
        # 编译工作流
        self.agent_system = self.workflow.compile(checkpointer=self.memory)
        
        logger.info("监督者工作流创建完成")
    
    async def process_message(self, message: str, user_id: str = "anonymous") -> AsyncGenerator[Tuple[str, Dict[str, Any]], None]:
        """
        处理用户消息并返回流式响应
        
        Args:
            message: 用户消息
            user_id: 用户ID
            
        Yields:
            Tuple[str, Dict]: (消息内容, 元数据)
        """
        try:
            logger.info(f"处理用户消息: {message[:50]}...")
            
            # 构建消息格式
            messages = [{"role": "user", "content": message}]
            
            # 流式处理消息
            final_message = ""
            async for message_chunk, metadata in self.agent_system.astream(
                {"messages": messages},
                stream_mode="messages",
                config={"configurable": {"thread_id": user_id}}
            ):
                if message_chunk.content:
                    final_message += message_chunk.content
                    yield message_chunk.content, metadata
            
            # 分析情绪
            emotion_result = await self.emotion_analyzer.analyze_emotion(message)
            
            # 返回最终结果和情绪信息
            yield "", {
                "type": "complete",
                "final_message": final_message,
                "emotion": emotion_result.feeling,
                "action": emotion_result.action,
                "voice_style": self.emotion_analyzer.get_voice_style(emotion_result.feeling)
            }
            
        except Exception as e:
            logger.error(f"消息处理失败: {e}")
            yield f"抱歉，处理您的消息时出现了错误: {str(e)}", {
                "type": "error",
                "emotion": "default",
                "action": "show-front-1",
                "voice_style": "chat"
            }
    
    def invoke(self, messages: List[Dict[str, str]], config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        同步调用智能体系统
        
        Args:
            messages: 消息列表
            config: 配置信息
            
        Returns:
            智能体响应
        """
        try:
            if config is None:
                config = {"configurable": {"thread_id": "anonymous"}}
            
            result = self.agent_system.invoke({"messages": messages}, config)
            return result
            
        except Exception as e:
            logger.error(f"同步调用失败: {e}")
            return {
                "messages": [{"role": "assistant", "content": f"抱歉，处理请求时出现错误: {str(e)}"}]
            }

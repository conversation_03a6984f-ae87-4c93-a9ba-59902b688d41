"""
多智能体系统工具模块

提供各种工具供智能体使用，包括：
- 搜索工具：用于获取实时信息
- 代码执行工具：用于运行Python代码
- RAG检索工具：用于检索知识库内容
"""

import os
import asyncio
from typing import Any, Dict, Optional
from langchain.agents import tool
import logging

# 导入可选依赖
try:
    from google_search_results import GoogleSearch
    HAS_SERP = True
except ImportError:
    try:
        from serpapi import GoogleSearch
        HAS_SERP = True
    except ImportError:
        HAS_SERP = False
        GoogleSearch = None

try:
    from langchain_community.tools.riza.command import ExecPython
    HAS_RIZA = True
except ImportError:
    HAS_RIZA = False
    ExecPython = None

logger = logging.getLogger(__name__)


class SearchTool:
    """搜索工具类，支持多种搜索引擎"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化搜索工具

        Args:
            config: 配置字典，包含API密钥等信息
        """
        self.config = config
        self.serp_api_key = os.getenv("SERP_API_KEY")
        self.name = "web_search"

    def search_web(self, query: str) -> str:
        """
        网络搜索工具，用于获取实时信息
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            搜索结果的文本摘要
        """
        try:
            if self.serp_api_key and HAS_SERP:
                # 使用SerpAPI进行搜索
                search = GoogleSearch({
                    "q": query,
                    "api_key": self.serp_api_key,
                    "num": 5,
                    "hl": "zh-cn",
                    "gl": "cn"
                })
                results = search.get_dict()

                if "organic_results" in results:
                    search_results = []
                    for result in results["organic_results"][:3]:
                        title = result.get("title", "")
                        snippet = result.get("snippet", "")
                        link = result.get("link", "")
                        search_results.append(f"标题: {title}\n摘要: {snippet}\n链接: {link}\n")

                    return "\n".join(search_results)
                else:
                    return "未找到相关搜索结果"
            else:
                # 如果没有API密钥或依赖，返回模拟结果
                if not HAS_SERP:
                    logger.warning("未安装搜索依赖包，返回模拟搜索结果")
                else:
                    logger.warning("未配置SERP_API_KEY，返回模拟搜索结果")
                return f"模拟搜索结果：关于'{query}'的相关信息。请配置SERP_API_KEY并安装相关依赖以获取真实搜索结果。"
                
        except Exception as e:
            logger.error(f"搜索工具执行失败: {e}")
            return f"搜索过程中出现错误: {str(e)}"


class CodeExecutionTool:
    """代码执行工具类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化代码执行工具

        Args:
            config: 配置字典
        """
        self.config = config
        self.riza_api_key = os.getenv("RIZA_API_KEY")
        self.name = "execute_python_code"

    def execute_python(self, code: str) -> str:
        """
        执行Python代码
        
        Args:
            code: 要执行的Python代码
            
        Returns:
            代码执行结果
        """
        try:
            if self.riza_api_key and HAS_RIZA:
                # 使用Riza执行代码
                exec_tool = ExecPython()
                result = exec_tool.run(code)
                return f"代码执行结果:\n{result}"
            else:
                # 如果没有API密钥或依赖，返回模拟结果
                if not HAS_RIZA:
                    logger.warning("未安装Riza依赖包，返回模拟执行结果")
                else:
                    logger.warning("未配置RIZA_API_KEY，返回模拟执行结果")
                return f"模拟代码执行结果：\n代码: {code}\n结果: 请配置RIZA_API_KEY并安装相关依赖以执行真实代码。"
                
        except Exception as e:
            logger.error(f"代码执行失败: {e}")
            return f"代码执行过程中出现错误: {str(e)}"


class RAGRetrievalTool:
    """RAG检索工具类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化RAG检索工具

        Args:
            config: 配置字典
        """
        self.config = config
        self.name = "retrieve_knowledge"

    def retrieve_knowledge(self, query: str) -> str:
        """
        从知识库检索相关信息
        
        Args:
            query: 检索查询
            
        Returns:
            检索到的相关文档内容
        """
        try:
            # 这里可以集成现有的RAG系统
            # 暂时返回模拟结果
            return f"从知识库检索到关于'{query}'的相关信息：\n这是一个模拟的检索结果，实际实现中会调用RAG系统。"
            
        except Exception as e:
            logger.error(f"知识库检索失败: {e}")
            return f"知识库检索过程中出现错误: {str(e)}"


def create_tools(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    创建所有工具实例
    
    Args:
        config: 配置字典
        
    Returns:
        工具实例字典
    """
    search_tool = SearchTool(config)
    code_tool = CodeExecutionTool(config)
    rag_tool = RAGRetrievalTool(config)
    
    return {
        "search": search_tool.search_web,
        "execute_python": code_tool.execute_python,
        "retrieve_knowledge": rag_tool.retrieve_knowledge
    }


# 全局用户存储
user_storage = {}

# 可添加一些辅助函数

def add_user(user_id, user_data):
    """
    添加或更新用户信息到全局用户存储。

    参数:
        user_id: 用户唯一标识
        user_data: 用户相关数据（可以是任意类型，如字典）
    """
    user_storage[user_id] = user_data


def get_user(user_id):
    """
    根据用户ID获取用户信息。

    参数:
        user_id: 用户唯一标识

    返回:
        用户数据（如果存在），否则返回None
    """
    return user_storage.get(user_id)


def get_all_users():
    """
    获取所有用户的存储信息。

    返回:
        包含所有用户数据的字典
    """
    return user_storage


def delete_user(user_id):
    """
    根据用户ID删除用户信息。

    参数:
        user_id: 用户唯一标识

    返回:
        如果删除成功返回True，否则返回False
    """
    if user_id in user_storage:
        del user_storage[user_id]
        return True
    return False